import sys
import socket
import threading
import cv2
import json
from numpy import False_, True_, signedinteger
import time
import sys, os
import datetime
import inspect
import ctypes
import random
import traceback
import math
import queue
from struct import pack , unpack
import config

class ManagementConnect(threading.Thread):
    def __init__(self,st_host,n_port, cameras, result_queue):
        threading.Thread.__init__(self, name="ManagementConnect")
        self.lock = threading.Lock()
        self.st_host = st_host
        self.n_port = n_port
        self.cameras = cameras
        # 队列
        self.result_queue = result_queue
        self.rqueue = queue.Queue()
        self.squeue = queue.Queue()
        self.sck = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        # 线程
        self.sendthread = threading.Thread(target=self.Sender, name='ManagementSender', daemon=True)
        self.state = threading.Condition()
        self.sendPaused = True

    def run(self):
        # 启动
        self.sendthread.start()
        self.doConnect()
        while True:
            try:
                # wait recv
                data = self.sck.recv(1024)
                if len(data):
                    #print('recv data:{}'.format(data))
                    # parse
                    msg = self.ParseJsonData(data)
                    if msg is not None:
                        if self.Execute(msg) == False:   
                            formatted_msg = json.dumps(msg)
                            self.rqueue.put(formatted_msg.encode())
                    #for msg in packages:
                    #    self.rqueue.put(msg)
            
            except OSError:
                traceback.print_exc()
                self.threadPause()
                time.sleep(2)
                print('connect to management error, doing connect in 2s ....')
                self.sck = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.doConnect()
            except Exception as e:
                print('other error occur:{}'.format(str(e)))
                traceback.print_exc()
                self.threadPause()
                time.sleep(4)
                self.doConnect()

    def doConnect(self):
        while True:
            try:
                #self.sck.settimeout(1)
                self.sck.connect((self.st_host, self.n_port))
                time.sleep(1)
                self.threadResume()
                print('connect to management {}:{}'.format(self.st_host,self.n_port))
                break
            except ConnectionRefusedError:
                print('management refused or not started, reconnect to management in 3s ...')
                time.sleep(3)

            except Exception as e:
                traceback.print_exc()
                print('do connect error:{}'.format(str(e)))
                time.sleep(5)

    def Sender(self):
        while True:
            # wait for resume
            with self.state:
                if self.sendPaused:
                    self.state.wait()
            try:
                msg = self.squeue.get()
                self.sck.send(msg)
            except OSError:
                traceback.print_exc()
                self.threadPause()
            except Exception as e:
                print('other error occur:{}'.format(str(e)))
                traceback.print_exc()
                self.threadPause()

    def threadResume(self):
        with self.state:
            self.sendPaused = False
            self.state.notify()

    def threadPause(self):
        with self.state:
            self.sendPaused = True

    def HeartBeat(self):
        try:
            data = {}
            data["eid"] = "VISUAL"
            data["dev"] = "VIS"
            data["type"] = "heartbeat"
            sendTime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            data["time"] = sendTime
            json_data = json.dumps(data)
            formatted_json = json.dumps(data, indent=4)#, sort_keys=True)
            print(formatted_json)
            self.squeue.put(json_data.encode())
        except TypeError:
            print("构建JSON数据失败,心跳")
    def SendMsg(self,data):
        try:
            json_data = json.dumps(data)
            formatted_json = json.dumps(data, indent=4)#, sort_keys=True)
            print(formatted_json)
            
            self.squeue.put(json_data.encode())
        except TypeError:
            print("构建JSON数据失败")
    def SendMsgQR(self,datas):
        try:
            reports = []
            reports.append(int(0x0E))
            AU_buffer = pack("!BBhhh", 1, 0,
                            int(datas["data"]["coordinates"][1]), int(datas["data"]["coordinates"][2]), int(datas["data"]["coordinates"][3])) #打包 大端模式
            for a in AU_buffer:
                reports.append(a)
            cnt = len(reports) + 1
            buffer = pack("!HHHHHH", 0x00AC, 0x0600, 0, 1, 2, cnt) #打包 大端模式
            data = []
            for a in buffer:
                data.append(a)
            for a in reports:
                data.append(a)
            #异或和校验
            sumA = 0
            for b in data:
                sumA ^= b
            data.append(sumA)
            msg = bytes(data)
            print(msg)
            self.squeue.put(msg)
        except TypeError:
            print("构建JSON数据失败")
    def StatusReport(self):
        try:
            data = {}
            data_task = {}
            data["eid"] = "VISUAL"
            data["dev"] = "VIS"
            data["type"] = "StatusReport"
            data["taskNum"] = "StatusReport"+time.strftime("%Y%m%d", time.localtime())+"000001"
            
            data_task["code"] = 0
            data_task["describe"] = "CameraOffline"
            data["data"] = data_task
            sendTime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            data["time"] = sendTime
            json_data = json.dumps(data)
            formatted_json = json.dumps(data, indent=4)#, sort_keys=True)
            print(formatted_json)
            self.squeue.put(json_data.encode())
        except TypeError:
            print("构建JSON数据失败,心跳")
        
    def StatusReporting(self,addr):
        result = ["fail","pass"]
        spindleHolder = ["existence","Non-existence"]
        try:
            data = {}
            data_task = {}
            data["eid"] = "VISUAL"
            if addr == "selfCheck":
                data["type"] = "selfCheckFeedback"
                data["taskNum"] = config.taskNum
                data_task["result"] = result[1]
                #待添加识别结果
                data_task["codeList"] = 0
                data["data"] = data_task

            elif addr == "topLevel":                
                data["type"] = "topLevelFeedback"
                data["taskNum"] = config.taskNum
                data_task["result"] = result[1]
                #待添加识别结果
                data_task["code"] = 0
                data_task["horizontalSide"] = [[0,0,300],[0,100,300]]
                data_task["verticalSide"] = [[0,0,300],[0,100,300]]
                data["data"] = data_task

            elif addr == "palletCenter":                
                data["type"] = "palletCenterFeedback"
                data["taskNum"] = config.taskNum
                data_task["result"] = result[1]
                #待添加识别结果
                data_task["code"] = 0
                data_task["PalletBottomCode"] = 231
                data_task["PalletTopCode"] = 233
                data_task["center"] = [0,100,300]
                data_task["angle"] = 0.5
                data["data"] = data_task

            elif addr == "placementLocation":                
                data["type"] = "placementLocationFeedback"
                data["taskNum"] = config.taskNum
                #待添加识别结果
                data_task["result"] = result[1]                
                data_task["code"] = 0
                data_task["point"] = [[0,100,300],[0,100,300],[0,100,300]]
                data["data"] = data_task

            elif addr == "foamSeparator":                
                data["type"] = "foamSeparatorFeedback"
                data["taskNum"] = config.taskNum
                #待添加识别结果
                data_task["result"] = result[1]                
                data_task["code"] = 0
                data_task["point"] = [0,100,300]
                data["data"] = data_task

            elif addr == "spindle":                
                data["type"] = "spindleFeedback"
                data["taskNum"] = config.taskNum
                #待添加识别结果
                data_task["result"] = result[1]                
                data_task["code"] = 0
                data_task["spindleHolder"] = spindleHolder[1]
                data_task["point"] = [0,100,300]
                data["data"] = data_task

            elif addr == "QRCode":                
                data["type"] = "QRCodeFeedback"
                data["taskNum"] = config.taskNum
                #待添加识别结果
                data_task["result"] = result[1]                
                data_task["code"] = 0
                data_task["pixel"] = [236,569]
                data_task["coordinates"] = [0,100,300]
                data_task["size"] = [100,100]
                data["data"] = data_task
            else:
                return

            sendTime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            data["sendTime"] = sendTime
            json_data = json.dumps(data)
            formatted_json = json.dumps(data, indent=4)#, sort_keys=True)
            print(formatted_json)
            self.squeue.put(json_data.encode())

        except TypeError:
            print("构建JSON数据失败")
    # 解析接收到的JSON数据
    def ParseJsonData(self,data):
        try:
            data = data.decode()
            json_data = json.loads(data)
            if "eid" in json_data:
                formatted_json = json.dumps(json_data, indent=4)#, sort_keys=True)
                print(formatted_json)
                config.log.logger.info(formatted_json)
                return json_data
            elif "result" in json_data:
                formatted_json = json.dumps(json_data, indent=4)#, sort_keys=True)
                print(formatted_json)
                return None
            else:
                print("缺少标识参数")
                return None
        except UnicodeDecodeError:
            print("UnicodeDecodeError,解析数据失败")
            return None
        except json.JSONDecodeError:
            print("解析数据失败")
            return None


    #状态初始化
    def Parametersinit(self):
        print("状态初始化")

    def CMDResp(self,cmd,ack,err):
        try:
            data = {}
            data["result"] = ack
            data["taskNum"] = cmd["taskNum"]
            sendTime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            data["time"] = sendTime
            if ack == True:
                data["code"] = 0
            else:
                data["code"] = err
            json_data = json.dumps(data)
            formatted_json = json.dumps(data, indent=4)#, sort_keys=True)
            print(formatted_json)
            self.squeue.put(json_data.encode())
        except TypeError:
            print("构建JSON数据失败")

    def Execute(self,msg):
        try:
            if msg["type"] == "revocation":
                self.CMDResp(msg,True,0)
                #待添加处理
                return True
            else:
                
                if msg['dev'] not in self.cameras:                    
                    self.CMDResp(msg,False,1004)
                    config.log.logger.info(f"1004 无设备 拒绝执行任务,{msg}")
                else:
                    if self.cameras[msg['dev']].offline :
                        self.CMDResp(msg,False,1003)
                        config.log.logger.info(f"1003 相机离线 拒绝执行任务,{msg}")
                    elif self.cameras[msg['dev']].isbusy:
                        self.CMDResp(msg,False,1001)
                        config.log.logger.info(f"1001 相机在忙 拒绝执行任务,{msg}")
                    else:                        
                        ret = self.cameras[msg['dev']].run_task(msg)
                        if ret == True:
                            self.CMDResp(msg,True,0)
                            config.log.logger.info(f"执行任务,{msg}")
                        else:
                            self.CMDResp(msg,False,1002)
                            config.log.logger.info(f"1002 执行任务失败,{msg}")
                return True

        except json.JSONDecodeError:
            print("解析JSON数据失败")
            return False
        except Exception as e:
            print('other error occur:{}'.format(str(e)))
            return False