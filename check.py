import pkg_resources

def check_requirements(requirements_file='requirements.txt'):
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = f.readlines()

    requirements = [line.strip() for line in requirements if line.strip() and not line.startswith('#')]

    not_installed = []
    for req in requirements:
        try:
            pkg_resources.require(req)
        except pkg_resources.DistributionNotFound:
            not_installed.append(f"未安装: {req}")
        except pkg_resources.VersionConflict as e:
            not_installed.append(f"版本不符: {req} (已安装: {e.dist.version})")
        except Exception as e:
            not_installed.append(f"检查 {req} 时出错: {e}")

    if not_installed:
        print("以下库未安装或版本不符：")
        for item in not_installed:
            print(item)
    else:
        print("requirements.txt 里的所有库都已正确安装！")

if __name__ == '__main__':
    check_requirements('requirements.txt')