#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化的错误直径返回功能
"""

def test_simple_diameter_error():
    """测试简化的错误直径信息返回"""
    print("=" * 60)
    print("测试简化的错误直径返回功能")
    print("=" * 60)
    
    # 模拟缺陷信息数据
    test_cases = [
        {
            "name": "情况1：没有任何缺陷",
            "defect_info_for_json": [],
            "expected": "没有"
        },
        {
            "name": "情况2：有直径异常的布捆",
            "defect_info_for_json": [
                {
                    'bundle_order': 2,
                    'defect_reasons': ['直径异常', '形状不规整'],
                    'defect_summary': '直径异常, 形状不规整'
                }
            ],
            "expected": "序号2是错误的"
        },
        {
            "name": "情况3：多个直径异常的布捆",
            "defect_info_for_json": [
                {
                    'bundle_order': 1,
                    'defect_reasons': ['直径超出范围'],
                    'defect_summary': '直径超出范围'
                },
                {
                    'bundle_order': 3,
                    'defect_reasons': ['直径异常'],
                    'defect_summary': '直径异常'
                }
            ],
            "expected": "序号1,3是错误的"
        },
        {
            "name": "情况4：只有形状异常，没有直径异常",
            "defect_info_for_json": [
                {
                    'bundle_order': 2,
                    'defect_reasons': ['形状不规整'],
                    'defect_summary': '形状不规整'
                }
            ],
            "expected": "没有"
        },
        {
            "name": "情况5：混合情况",
            "defect_info_for_json": [
                {
                    'bundle_order': 1,
                    'defect_reasons': ['形状不规整'],
                    'defect_summary': '形状不规整'
                },
                {
                    'bundle_order': 2,
                    'defect_reasons': ['直径异常'],
                    'defect_summary': '直径异常'
                },
                {
                    'bundle_order': 4,
                    'defect_reasons': ['直径超出范围', '形状不规整'],
                    'defect_summary': '直径超出范围, 形状不规整'
                }
            ],
            "expected": "序号2,4是错误的"
        }
    ]
    
    # 模拟frame_get.py中的逻辑
    def get_error_diameter_info(defect_info_for_json):
        """获取错误直径信息"""
        error_diameter_info = "没有"
        if defect_info_for_json:
            error_orders = []
            for defect in defect_info_for_json:
                if any("直径" in reason for reason in defect['defect_reasons']):
                    error_orders.append(str(defect['bundle_order']))
            if error_orders:
                error_diameter_info = f"序号{','.join(error_orders)}是错误的"
        return error_diameter_info
    
    # 执行测试
    all_passed = True
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- {test_case['name']} ---")
        
        # 打印输入数据
        if test_case['defect_info_for_json']:
            print("输入缺陷信息:")
            for defect in test_case['defect_info_for_json']:
                print(f"  序号{defect['bundle_order']}: {defect['defect_reasons']}")
        else:
            print("输入缺陷信息: 无")
        
        # 执行逻辑
        result = get_error_diameter_info(test_case['defect_info_for_json'])
        
        # 验证结果
        if result == test_case['expected']:
            print(f"✓ 测试通过: 错误直径为 = '{result}'")
        else:
            print(f"❌ 测试失败: 期望 '{test_case['expected']}', 实际 '{result}'")
            all_passed = False
    
    # 模拟完整的JSON消息
    print(f"\n--- 完整JSON消息示例 ---")
    
    # 示例：有错误直径的情况
    example_defect_info = [
        {
            'bundle_order': 2,
            'defect_reasons': ['直径异常', '形状不规整'],
            'defect_summary': '直径异常, 形状不规整'
        }
    ]
    
    error_diameter_info = get_error_diameter_info(example_defect_info)
    
    example_json = {
        "type": "clothFeedback",
        "taskNum": "test_task_001",
        "data": {
            "result": "pass",
            "code": 0,
            "points": [
                [100, 200, 300, 45, 1, 250, 800],  # 正常布捆
                [150, 250, 320, 50, 2, 383, 850],  # 异常布捆（直径383）
            ],
            "nextPoints": [],
            "错误直径为": error_diameter_info
        },
        "sendTime": "2024-01-01 12:00:00"
    }
    
    import json
    print(json.dumps(example_json, ensure_ascii=False, indent=2))
    
    return all_passed

if __name__ == "__main__":
    success = test_simple_diameter_error()
    
    if success:
        print("\n🎉 简化的错误直径返回功能测试成功！")
        print("\n📋 实现说明:")
        print("1. ✓ 在JSON返回中添加了'错误直径为'字段")
        print("2. ✓ 如果有直径异常的布捆，返回'序号X是错误的'")
        print("3. ✓ 如果没有直径异常，返回'没有'")
        print("4. ✓ 支持多个异常布捆的情况，用逗号分隔序号")
        print("5. ✓ 只检查包含'直径'关键字的缺陷原因")
    else:
        print("\n❌ 简化的错误直径返回功能测试失败！")
