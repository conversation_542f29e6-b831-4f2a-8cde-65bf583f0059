#yoloonxxxxxxxx
import cv2
import numpy as np
import onnxruntime as ort
import time
import math
import os
import glob

classes = {0: 'box', 1: 'paper', 2: 'bag'}

class Colors:
    """
    This class provides methods to work with the Ultralytics color palette, including converting hex color codes to
    RGB values.

    Attributes:
        palette (list of tuple): List of RGB color values.
        n (int): The number of colors in the palette.
        pose_palette (np.array): A specific color palette array with dtype np.uint8.
    """

    def __init__(self):
        """Initialize colors as hex = matplotlib.colors.TABLEAU_COLORS.values()."""
        hexs = ('FF3838', 'FF9D97', 'FF701F', 'FFB21D', 'CFD231', '48F90A', '92CC17', '3DDB86', '1A9334', '00D4BB',
                '2C99A8', '00C2FF', '344593', '6473FF', '0018EC', '8438FF', '520085', 'CB38FF', 'FF95C8', 'FF37C7')
        self.palette = [self.hex2rgb(f'#{c}') for c in hexs]
        self.n = len(self.palette)
        self.pose_palette = np.array([[255, 128, 0], [255, 153, 51], [255, 178, 102], [230, 230, 0], [255, 153, 255],
                                        [153, 204, 255], [255, 102, 255], [255, 51, 255], [102, 178, 255], [51, 153, 255],
                                        [255, 153, 153], [255, 102, 102], [255, 51, 51], [153, 255, 153], [102, 255, 102],
                                        [51, 255, 51], [0, 255, 0], [0, 0, 255], [255, 0, 0], [255, 255, 255]],
                                        dtype=np.uint8)

    def __call__(self, i, bgr=False):
        """Converts hex color codes to RGB values."""
        c = self.palette[int(i) % self.n]
        return (c[2], c[1], c[0]) if bgr else c

    @staticmethod
    def hex2rgb(h):
        """Converts hex color codes to RGB values (i.e. default PIL order)."""
        return tuple(int(h[1 + i:1 + i + 2], 16) for i in (0, 2, 4))


class YOLOv8Seg:
    """YOLOv8 segmentation model."""

    def __init__(self, onnx_model):
        """
        Initialization.

        Args:
            onnx_model (str): Path to the ONNX model.
        """

        # Build Ort session
        self.session = ort.InferenceSession(onnx_model,
                                            providers=['CUDAExecutionProvider', 'CPUExecutionProvider']
                                            if ort.get_device() == 'GPU' else ['CPUExecutionProvider'])

        # Numpy dtype: support both FP32 and FP16 onnx model
        self.ndtype = np.half if self.session.get_inputs()[0].type == 'tensor(float16)' else np.single

        # Get model width and height(YOLOv8-seg only has one input)
        self.model_height, self.model_width = [x.shape for x in self.session.get_inputs()][0][-2:]

        # Load COCO class names
        self.classes = classes

        # Create color palette
        self.color_palette = Colors()

    def __call__(self, im0, conf_threshold=0.4, iou_threshold=0.45, nm=32):
        """
        The whole pipeline: pre-process -> inference -> post-process.

        Args:
            im0 (Numpy.ndarray): original input image.
            conf_threshold (float): confidence threshold for filtering predictions.
            iou_threshold (float): iou threshold for NMS.
            nm (int): the number of masks.

        Returns:
            boxes (List): list of bounding boxes.
            segments (List): list of segments.
            masks (np.ndarray): [N, H, W], output masks.
        """

        # Pre-process
        im, ratio, (pad_w, pad_h) = self.preprocess(im0)

        # Ort inference
        preds = self.session.run(None, {self.session.get_inputs()[0].name: im})

        # Post-process
        boxes, segments, masks = self.postprocess(preds,
                                                    im0=im0,
                                                    ratio=ratio,
                                                    pad_w=pad_w,
                                                    pad_h=pad_h,
                                                    conf_threshold=conf_threshold,
                                                    iou_threshold=iou_threshold,
                                                    nm=nm)
        return boxes, segments, masks

    def preprocess(self, img):
        """
        Pre-processes the input image.

        Args:
            img (Numpy.ndarray): image about to be processed.

        Returns:
            img_process (Numpy.ndarray): image preprocessed for inference.
            ratio (tuple): width, height ratios in letterbox.
            pad_w (float): width padding in letterbox.
            pad_h (float): height padding in letterbox.
        """

        # Resize and pad input image using letterbox() (Borrowed from Ultralytics)
        shape = img.shape[:2]  # original image shape
        new_shape = (self.model_height, self.model_width)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        ratio = r, r
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        pad_w, pad_h = (new_shape[1] - new_unpad[0]) / 2, (new_shape[0] - new_unpad[1]) / 2  # wh padding
        if shape[::-1] != new_unpad:  # resize
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(pad_h - 0.1)), int(round(pad_h + 0.1))
        left, right = int(round(pad_w - 0.1)), int(round(pad_w + 0.1))
        img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(114, 114, 114))

        # Transforms: HWC to CHW -> BGR to RGB -> div(255) -> contiguous -> add axis(optional)
        img = np.ascontiguousarray(np.einsum('HWC->CHW', img)[::-1], dtype=self.ndtype) / 255.0
        img_process = img[None] if len(img.shape) == 3 else img
        return img_process, ratio, (pad_w, pad_h)

    def postprocess(self, preds, im0, ratio, pad_w, pad_h, conf_threshold, iou_threshold, nm=32):
        """
        Post-process the prediction.

        Args:
            preds (Numpy.ndarray): predictions come from ort.session.run().
            im0 (Numpy.ndarray): [h, w, c] original input image.
            ratio (tuple): width, height ratios in letterbox.
            pad_w (float): width padding in letterbox.
            pad_h (float): height padding in letterbox.
            conf_threshold (float): conf threshold.
            iou_threshold (float): iou threshold.
            nm (int): the number of masks.

        Returns:
            boxes (List): list of bounding boxes.
            segments (List): list of segments.
            masks (np.ndarray): [N, H, W], output masks.
        """
        x, protos = preds[0], preds[1]  # Two outputs: predictions and protos

        # Transpose the first output: (Batch_size, xywh_conf_cls_nm, Num_anchors) -> (Batch_size, Num_anchors, xywh_conf_cls_nm)
        x = np.einsum('bcn->bnc', x)

        # Predictions filtering by conf-threshold
        x = x[np.amax(x[..., 4:-nm], axis=-1) > conf_threshold]

        # Create a new matrix which merge these(box, score, cls, nm) into one
        # For more details about `numpy.c_()`: https://numpy.org/doc/1.26/reference/generated/numpy.c_.html
        x = np.c_[x[..., :4], np.amax(x[..., 4:-nm], axis=-1), np.argmax(x[..., 4:-nm], axis=-1), x[..., -nm:]]
        # NMS filtering
        x = x[cv2.dnn.NMSBoxes(x[:, :4], x[:, 4], conf_threshold, iou_threshold)]

        # Decode and return
        if len(x) > 0:

            # Bounding boxes format change: cxcywh -> xyxy
            x[..., [0, 1]] -= x[..., [2, 3]] / 2
            x[..., [2, 3]] += x[..., [0, 1]]

            # Rescales bounding boxes from model shape(model_height, model_width) to the shape of original image
            x[..., :4] -= [pad_w, pad_h, pad_w, pad_h]
            x[..., :4] /= min(ratio)

            # Bounding boxes boundary clamp
            x[..., [0, 2]] = x[:, [0, 2]].clip(0, im0.shape[1])
            x[..., [1, 3]] = x[:, [1, 3]].clip(0, im0.shape[0])

            # Process masks
            masks = self.process_mask(protos[0], x[:, 6:], x[:, :4], im0.shape)

            # Masks -> Segments(contours)
            segments = self.masks2segments(masks)
            
            # 确保返回的boxes是列表格式，而不是NumPy数组
            boxes = x[..., :6].tolist() if hasattr(x[..., :6], 'tolist') else list(x[..., :6])
            
            return boxes, segments, masks  # boxes, segments, masks
        else:
            return [], [], []

    @staticmethod
    def masks2segments(masks):
        """
        It takes a list of masks(n,h,w) and returns a list of segments(n,xy)
        Args:
            masks (numpy.ndarray): the output of the model, which is a tensor of shape (batch_size, 160, 160).

        Returns:
            segments (List): list of segment masks.
        """
        segments = []
        for x in masks.astype('uint8'):
            c = cv2.findContours(x, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)[0]  # CHAIN_APPROX_SIMPLE
            if c:
                c = np.array(c[np.array([len(x) for x in c]).argmax()]).reshape(-1, 2)
            else:
                c = np.zeros((0, 2))  # no segments found
            segments.append(c.astype('float32'))
        return segments

    @staticmethod
    def crop_mask(masks, boxes):
        """
        It takes a mask and a bounding box, and returns a mask that is cropped to the bounding box.
        Args:
            masks (Numpy.ndarray): [n, h, w] tensor of masks.
            boxes (Numpy.ndarray): [n, 4] tensor of bbox coordinates in relative point form.

        Returns:
            (Numpy.ndarray): The masks are being cropped to the bounding box.
        """
        n, h, w = masks.shape
        x1, y1, x2, y2 = np.split(boxes[:, :, None], 4, 1)
        r = np.arange(w, dtype=x1.dtype)[None, None, :]
        c = np.arange(h, dtype=x1.dtype)[None, :, None]
        return masks * ((r >= x1) * (r < x2) * (c >= y1) * (c < y2))

    def process_mask(self, protos, masks_in, bboxes, im0_shape):
        """
        Takes the output of the mask head, and applies the mask to the bounding boxes. This produces masks of higher quality
        but is slower.
        Args:
            protos (numpy.ndarray): [mask_dim, mask_h, mask_w].
            masks_in (numpy.ndarray): [n, mask_dim], n is number of masks after nms.
            bboxes (numpy.ndarray): bboxes re-scaled to original image shape.
            im0_shape (tuple): the size of the input image (h,w,c).

        Returns:
            (numpy.ndarray): The upsampled masks.
        """
        c, mh, mw = protos.shape
        masks = np.matmul(masks_in, protos.reshape((c, -1))).reshape((-1, mh, mw)).transpose(1, 2, 0)  # HWN
        masks = np.ascontiguousarray(masks)
        masks = self.scale_mask(masks, im0_shape)  # re-scale mask from P3 shape to original input image shape
        masks = np.einsum('HWN -> NHW', masks)  # HWN -> NHW
        masks = self.crop_mask(masks, bboxes)
        return np.greater(masks, 0.5)

    @staticmethod
    def scale_mask(masks, im0_shape, ratio_pad=None):
        """
        Takes a mask, and resizes it to the original image size.
        Args:
            masks (np.ndarray): resized and padded masks/images, [h, w, num]/[h, w, 3].
            im0_shape (tuple): the original image shape.
            ratio_pad (tuple): the ratio of the padding to the original image.

        Returns:
            masks (np.ndarray): The masks that are being returned.
        """
        im1_shape = masks.shape[:2]
        if ratio_pad is None:  # calculate from im0_shape
            gain = min(im1_shape[0] / im0_shape[0], im1_shape[1] / im0_shape[1])  # gain  = old / new
            pad = (im1_shape[1] - im0_shape[1] * gain) / 2, (im1_shape[0] - im0_shape[0] * gain) / 2  # wh padding
        else:
            pad = ratio_pad[1]

        # Calculate tlbr of mask
        top, left = int(round(pad[1] - 0.1)), int(round(pad[0] - 0.1))  # y, x
        bottom, right = int(round(im1_shape[0] - pad[1] + 0.1)), int(round(im1_shape[1] - pad[0] + 0.1))
        if len(masks.shape) < 2:
            raise ValueError(f'"len of masks shape" should be 2 or 3, but got {len(masks.shape)}')
        masks = masks[top:bottom, left:right]
        masks = cv2.resize(masks, (im0_shape[1], im0_shape[0]),
                            interpolation=cv2.INTER_LINEAR)  # INTER_CUBIC would be better
        if len(masks.shape) == 2:
            masks = masks[:, :, None]
        return masks

    def draw_and_visualize(self, im, bboxes, segments, depthimg, depth_calib_intr, vis=True, save=False, xyz_range=None, grasping_order_map=None):
        """
        Draw and visualize results.

        Args:
            im (np.ndarray): original image, shape [h, w, c].
            bboxes (numpy.ndarray): [n, 4], n is number of bboxes.
            segments (List): list of segment masks.
            depthimg: depth image for 3D coordinate calculation
            depth_calib_intr: depth camera intrinsic parameters
            vis (bool): imshow using OpenCV.
            save (bool): save image annotated.
            xyz_range: range filter for xyz coordinates
            grasping_order_map: mapping of box index to grasping order

        Returns:
            im: annotated image
            result: detection results
        """
        try:
            print(f"\n[YOLO调试] ====== draw_and_visualize开始 ======")
            print(f"[YOLO调试] 接收到的grasping_order_map: {grasping_order_map}")
            print(f"[YOLO调试] 检测到{len(bboxes)}个目标")
            print(f"[YOLO调试] 图像尺寸: {im.shape if im is not None else 'None'}")
            print(f"[YOLO调试] 深度图尺寸: {depthimg.shape if depthimg is not None else 'None'}")
            
            result = []
            defect_list = []  # 初始化缺陷列表
            # Draw rectangles and polygons
            im_canvas = im.copy()
            for idx, ((*box, conf, cls_), segment) in enumerate(zip(bboxes, segments)):
                print(f"\n[Box {idx}] ----")
                segment_np = np.array(segment, dtype=np.int32)
                if segment_np.ndim == 2:
                    segment_np = segment_np.reshape(-1, 1, 2)
                cv2.polylines(im, [segment_np], True, (255, 255, 255), 2)
                cv2.fillPoly(im_canvas, [segment_np], self.color_palette(int(cls_), bgr=True))
                rect = cv2.minAreaRect(np.array(segment))
                width = rect[1][0]
                height = rect[1][1]
                shot_edge = min(width, height)
                if width > height:
                    long_edge = width
                    angle = rect[2]
                else:
                    long_edge = height
                    angle = rect[2] - 90
                if angle < -90:
                    angle += 90
                minbox = cv2.boxPoints(rect)
                minbox = np.array(minbox, dtype=np.int32)
                center = (int(rect[0][0]), int(rect[0][1]))
                # 修正：确保center_depth为float
                if depthimg is not None:
                    if 0 <= center[1] < depthimg.shape[0] and 0 <= center[0] < depthimg.shape[1]:
                        center_depth = depthimg[center[1], center[0]]
                        if isinstance(center_depth, np.ndarray):
                            if center_depth.size == 1:
                                center_depth = float(center_depth[0])
                            else:
                                center_depth = float(center_depth.flatten()[0])
                        else:
                            center_depth = float(center_depth)
                    else:
                        center_depth = None
                else:
                    center_depth = None
                if center[0] < depthimg.shape[1]*0.1 or center[0] > depthimg.shape[1] * 0.9 :
                    continue
                if center[1] < depthimg.shape[0]*0.1 or center[1] > depthimg.shape[0] * 0.9 :
                    continue
                midpoint1 = ((int(minbox[0][0]) + int(minbox[1][0])) // 2, (int(minbox[0][1]) + int(minbox[1][1])) // 2)
                midpoint2 = ((int(minbox[2][0]) + int(minbox[3][0])) // 2, (int(minbox[2][1]) + int(minbox[3][1])) // 2)
                midpoint3 = ((int(minbox[0][0]) + int(minbox[3][0])) // 2, (int(minbox[0][1]) + int(minbox[3][1])) // 2)
                midpoint4 = ((int(minbox[2][0]) + int(minbox[1][0])) // 2, (int(minbox[2][1]) + int(minbox[1][1])) // 2)
                center_text_x = f"x: {center[0]}"
                center_text_y = f"y: {center[1]}"
                cv2.putText(im, center_text_x, (int(center[0]) + 5, int(center[1])-20), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2)
                cv2.putText(im, center_text_y, (int(center[0]) + 5, int(center[1])- 5), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2)
                angle_text = f"Angle:{angle:.2f}"
                cv2.putText(im, angle_text, (int(box[0]), int(box[1])-10), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 0), 2)
                if xyz_range is not None:
                    x_min, x_max, y_min, y_max, z_min, z_max = xyz_range
                    x,y,z = self.get_point(center[0], center[1], depthimg, depth_calib_intr,z_range = [z_min,z_max])
                else:
                    x,y,z = self.get_point(center[0], center[1], depthimg, depth_calib_intr)
                if x is None or y is None or z is None:
                    continue
                # 只为grasping_order_map中有编号的目标画编号
                if grasping_order_map is not None:
                    print(f"[YOLO调试] idx={idx}, grasping_order_map.keys()={list(grasping_order_map.keys())}")
                grasping_order_text = ""
                if grasping_order_map is not None and idx in grasping_order_map:
                    order_num = grasping_order_map[idx]
                    grasping_order_text = f"#{order_num}"
                    text_x = int(center[0]) + 360
                    text_y = int(center[1])
                    # 判断y是否为边沿布捆
                    color = (255, 0, 0) if abs(y) > 450 else (0, 0, 255)  # 蓝色 or 红色
                    cv2.putText(im, grasping_order_text, (text_x, text_y), 
                               cv2.FONT_HERSHEY_SIMPLEX, 1.5, color, 3)
                    cv2.circle(im, (text_x + 15, text_y - 10), 25, color, 2)
                mask = np.zeros(im.shape[:2], dtype=np.uint8)
                cv2.drawContours(mask, [np.array(segment, dtype=np.int32)], 0, (1,1,1), -1)
                mask_h, mask_w = mask.shape
                rect_center = np.array(rect[0])
                rect_size = rect[1]
                rect_angle = rect[2]
                if rect_size[0] < rect_size[1]:
                    theta = np.deg2rad(rect_angle)
                else:
                    theta = np.deg2rad(rect_angle + 90)
                normal_vec = np.array([np.cos(theta), np.sin(theta)])
                tangent_vec = np.array([-normal_vec[1], normal_vec[0]])
                sample_points = [
                    rect_center,
                    rect_center + tangent_vec * 50,
                    rect_center - tangent_vec * 50
                ]
                diameters_pixel = []
                centers_2d = []
                for idx2, pt in enumerate(sample_points):
                    pt = np.clip(pt, [0,0], [mask_w-1, mask_h-1])
                    def find_edge_along_vec(center, vec, mask, max_search=300):
                        base = np.array(center, dtype=np.float32)
                        for d in range(0, max_search):
                            p = base + vec * d
                            x, y = int(round(p[0])), int(round(p[1]))
                            if x < 0 or x >= mask_w or y < 0 or y >= mask_h or mask[y, x] == 0:
                                return (int(np.clip(round(base[0] + vec[0]*(d-1)), 0, mask_w-1)), int(np.clip(round(base[1] + vec[1]*(d-1)), 0, mask_h-1)))
                        return (int(np.clip(round(base[0] + vec[0]*max_search), 0, mask_w-1)), int(np.clip(round(base[1] + vec[1]*max_search), 0, mask_h-1)))
                    pt_pos = find_edge_along_vec(pt, normal_vec, mask)
                    pt_neg = find_edge_along_vec(pt, -normal_vec, mask)
                    color = (0,0,255) if idx2==0 else (255,0,0)
                    cv2.line(im, pt_pos, pt_neg, color, 3)
                    cv2.circle(im, pt_pos, 8, (0,255,0), -1)      # 绿色圆点表示pt_pos
                    cv2.circle(im, pt_neg, 8, (0,165,255), -1)    # 橙色圆点表示pt_neg
                    center_pt = (int((pt_pos[0] + pt_neg[0]) / 2), int((pt_pos[1] + pt_neg[1]) / 2))
                    centers_2d.append(center_pt)
                    dx = pt_pos[0] - pt_neg[0]
                    dy = pt_pos[1] - pt_neg[1]
                    pixel_dist = np.sqrt(dx*dx + dy*dy)
                    print(f"[YOLO直径调试] Box{idx} 采样点{idx2}: pt_pos=({pt_pos[0]},{pt_pos[1]}), pt_neg=({pt_neg[0]},{pt_neg[1]})")
                    print(f"[YOLO直径调试] Box{idx} 采样点{idx2}: dx={dx}, dy={dy}, 像素距离={pixel_dist:.2f}")
                    if center_depth is not None and center_depth > 0:
                        fx = float(depth_calib_intr[0])
                        diam_pixel = float(pixel_dist) * float(center_depth) / fx
                        print(f"[YOLO直径调试] Box{idx} 采样点{idx2}: center_depth={center_depth}, fx={fx}")
                        print(f"[YOLO直径调试] Box{idx} 采样点{idx2}: 物理直径计算 = {pixel_dist:.2f} * {center_depth} / {fx} = {diam_pixel:.2f}")
                        diameters_pixel.append(diam_pixel)
                        if idx2 != 0:
                            cv2.putText(im, f"{int(round(diam_pixel))}", (center_pt[0]+5, center_pt[1]-5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255,0,0), 2)
                    else:
                        print(f"[YOLO直径调试] Box{idx} 采样点{idx2}: center_depth无效({center_depth})，跳过计算")
                        diameters_pixel.append(0)
                valid_diams = [d for d in diameters_pixel if d > 0]
                print(f"[YOLO直径调试] Box{idx}: 所有采样点直径={diameters_pixel}")
                print(f"[YOLO直径调试] Box{idx}: 有效直径={valid_diams}")
                avg_diameter_pixel = int(round(sum(valid_diams)/len(valid_diams))) if valid_diams else 0
                print(f"[YOLO直径调试] Box{idx}: 平均直径={avg_diameter_pixel}")
                avg_center_2d = tuple(int(round(x)) for x in np.mean(centers_2d, axis=0))

                # 布捆缺损检测：双重检测方案
                print(f"[布捆缺损检测] Box{idx}: 开始双重缺损检测，直径={avg_diameter_pixel}mm")
                if avg_diameter_pixel > 200:  # 只对足够大的布捆进行检测

                    def calculate_shape_irregularity(mask, rect_center, rect_size, rect_angle):
                        """计算布捆形状不规整程度"""
                        # 创建理论矩形mask
                        theoretical_mask = np.zeros_like(mask)
                        rect_points = cv2.boxPoints(((rect_center[0], rect_center[1]), rect_size, rect_angle))
                        rect_points = np.int0(rect_points)
                        cv2.fillPoly(theoretical_mask, [rect_points], 1)

                        # 计算实际mask与理论mask的差异
                        actual_area = np.sum(mask)
                        theoretical_area = np.sum(theoretical_mask)
                        intersection_area = np.sum(mask & theoretical_mask)

                        # 计算IoU (Intersection over Union)
                        union_area = actual_area + theoretical_area - intersection_area
                        iou = intersection_area / union_area if union_area > 0 else 0

                        # 形状规整度 = IoU，越接近1越规整
                        shape_regularity = iou

                        return shape_regularity, actual_area, theoretical_area, intersection_area

                    # 计算形状规整度
                    shape_regularity, actual_area, theoretical_area, intersection_area = calculate_shape_irregularity(mask, rect_center, rect_size, rect_angle)

                    print(f"[形状检测] Box{idx}: 实际面积={actual_area}px², 理论面积={theoretical_area}px²")
                    print(f"[形状检测] Box{idx}: 交集面积={intersection_area}px², 形状规整度={shape_regularity:.3f}")

                    # 如果形状规整度太低，说明布捆形状不规则
                    shape_threshold = 0.85  # 形状规整度阈值

                    # 双重检测：形状规整度 + 直径合理性
                    # 直径合理性检测
                    min_diameter = 200  # 最小合理直径(mm)
                    max_diameter = 320  # 最大合理直径(mm)
                    diameter_reasonable = min_diameter <= avg_diameter_pixel <= max_diameter

                    shape_ok = shape_regularity >= shape_threshold

                    print(f"[综合判断] Box{idx}: 形状规整度检测={'✓' if shape_ok else '✗'}({shape_regularity:.3f}>={shape_threshold})")
                    print(f"[综合判断] Box{idx}: 直径合理性检测={'✓' if diameter_reasonable else '✗'}({avg_diameter_pixel}mm, 范围:{min_diameter}-{max_diameter}mm)")

                    # 记录缺陷信息
                    grasping_order = grasping_order_map.get(idx, 0) if grasping_order_map else 0

                    if not shape_ok or not diameter_reasonable:
                        defect_reasons = []
                        if not shape_ok:
                            defect_reasons.append("形状不规整")
                        if not diameter_reasonable:
                            defect_reasons.append("直径异常")

                        print(f"[布捆缺损检测] Box{idx}: ⚠️ 检测到缺损！原因: {', '.join(defect_reasons)}")

                        # 添加到缺陷列表
                        defect_info = {
                            'has_defect': True,
                            'defect_reasons': defect_reasons,
                            'grasping_order': grasping_order,
                            'shape_regularity': shape_regularity,
                            'diameter_mm': avg_diameter_pixel
                        }
                        defect_list.append(defect_info)

                        # 在红色序号旁边画绿色的×
                        if grasping_order_map is not None and idx in grasping_order_map:
                            # 使用与红色序号相同的位置
                            text_x = int(center[0]) + 360
                            text_y = int(center[1])
                            x_size = 20  # ×的大小

                            # 在红色序号右侧画绿色的×
                            cross_x = text_x + 80  # 在序号右侧80像素处
                            cross_y = text_y

                            cv2.line(im, (cross_x - x_size, cross_y - x_size), (cross_x + x_size, cross_y + x_size), (0, 255, 0), 4)
                            cv2.line(im, (cross_x - x_size, cross_y + x_size), (cross_x + x_size, cross_y - x_size), (0, 255, 0), 4)
                        else:
                            # 如果没有序号，则在直径数字旁边画×（备用方案）
                            x_center = int(avg_center_2d[0])
                            y_center = int(avg_center_2d[1])
                            x_size = 15
                            cv2.line(im, (x_center + 30, y_center - x_size), (x_center + 30 + x_size*2, y_center + x_size), (0, 255, 0), 4)
                            cv2.line(im, (x_center + 30, y_center + x_size), (x_center + 30 + x_size*2, y_center - x_size), (0, 255, 0), 4)
                    else:
                        print(f"[布捆缺损检测] Box{idx}: ✓ 双重检测通过，布捆完整")

                        # 添加正常布捆信息
                        defect_info = {
                            'has_defect': False,
                            'defect_reasons': [],
                            'grasping_order': grasping_order,
                            'shape_regularity': shape_regularity,
                            'diameter_mm': avg_diameter_pixel
                        }
                        defect_list.append(defect_info)
                else:
                    # 直径太小，不进行缺陷检测，默认为正常
                    grasping_order = grasping_order_map.get(idx, 0) if grasping_order_map else 0
                    defect_info = {
                        'has_defect': False,
                        'defect_reasons': [],
                        'grasping_order': grasping_order,
                        'shape_regularity': 1.0,  # 默认形状规整
                        'diameter_mm': avg_diameter_pixel
                    }
                    defect_list.append(defect_info)
                    print(f"[布捆缺损检测] Box{idx}: 直径太小({avg_diameter_pixel}mm)，跳过缺陷检测")

                cv2.circle(im, avg_center_2d, 10, (0,0,255), -1)
                cv2.putText(im, f"{avg_diameter_pixel}", (avg_center_2d[0]+5, avg_center_2d[1]-5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2)
                cv2.putText(im, f"({x},{y},{z})", (avg_center_2d[0]+5, avg_center_2d[1]+25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2)
                result.append([x, y, z, -angle, int(avg_center_2d[0]), int(avg_center_2d[1]), avg_diameter_pixel, long_edge])
            im = cv2.addWeighted(im_canvas, 0.3, im, 0.9, 0)
            im = np.clip(im, 0, 255).astype(np.uint8)
            print(f"[YOLO调试] draw_and_visualize完成，返回{len(result)}个结果")
            print(f"[YOLO调试] 结果详情: {result}")
            print(f"[YOLO调试] 缺陷信息: {defect_list}")
            return im, result, defect_list
        except Exception as e:
            print("Error task_box_process_yolo: %s", e)
            print(f"[YOLO调试] draw_and_visualize发生异常: {e}")
            return im, None, []
    def get_depthimg(self):
        depth_image = cv2.imread('depth.png', cv2.IMREAD_UNCHANGED)
        depth_data = np.asanyarray(depth_image)
        return depth_data
    
    def get_point(self, px, py, depthimg, depth_calib_intr,z_range = [1800, 4000]):
        depth = self.regional_depth_get(depthimg,[[px-10, py-10], [px+10, py+10]], [z_range[0], z_range[1]])
        if depth is None:
            return [None,None,None]
        return self.depth2xyz(px, py, depth, depth_calib_intr)
    def get_valid_depth(self, depthimg, pt, search_radius=3):
        """
        在pt点附近自动寻找最近的非零深度，优先返回自身，否则在半径范围内搜索。
        """
        x, y = pt
        h, w = depthimg.shape
        # 先检查自身
        if 0 <= x < w and 0 <= y < h:
            d = depthimg[y, x]
            if d > 0:
                return d
        # 再在邻域内搜索
        for r in range(1, search_radius+1):
            for dx in range(-r, r+1):
                for dy in range(-r, r+1):
                    nx, ny = x+dx, y+dy
                    if 0 <= nx < w and 0 <= ny < h:
                        d = depthimg[ny, nx]
                        if d > 0:
                            return d
        return 0  # 没找到
    def regional_depth_get(self, depthImg, roi, depthRange):
        try:
            depthRoi = depthImg[roi[0][1]:roi[1][1], roi[0][0]:roi[1][0]]
            depthMask = (depthRoi > depthRange[0]) & (depthRoi < depthRange[1])
            depthRoiScaled = depthRoi[depthMask] * 1
            depthList = np.array(depthRoiScaled)
            listLength = len(depthList)
            sortDepthList = np.sort(depthList)[int(listLength * 0.1):int(listLength * 0.3)]
            if math.isnan(np.mean(sortDepthList)):
                return None
            else:
                #std = np.std(depthList, ddof=1)
                return int(np.mean(sortDepthList))
        except:
            return None
    def depth2xyz(self, px, py, depth, depth_calib_intr):
        cx = depth_calib_intr[2]#663.191772
        cy = depth_calib_intr[5]#494.933533
        fx = depth_calib_intr[0]#1044.018799
        fy = depth_calib_intr[4]#1044.018799
        z = int(depth)
        x = int((px - cx) * z / fx)
        y = int((py - cy) * z / fy)
        print(f"[YOLO坐标转换调试] 像素坐标({px},{py}) -> 物理坐标({x},{y},{z})")
        print(f"[YOLO坐标转换调试] 内参: cx={cx}, cy={cy}, fx={fx}, fy={fy}, depth={depth}")
        print(f"[YOLO坐标转换调试] 计算公式: x=({px}-{cx})*{z}/{fx}={x}, y=({py}-{cy})*{z}/{fy}={y}")
        result = [x, y, z]
        return result


# 设置文件夹路径和文件扩展名
#folder_path = 'E:\\label\\images'
folder_path = 'D:\\BaiduNetdiskDownload\\yolov8_seg\\img'
#folder_path = 'D:\\yolov8\\ptzxcd\\images'
file_extension = '.png' #'.png'

# 获取文件夹内所有.jpg文件的列表
def get_files():
    return glob.glob(os.path.join(folder_path, '*' + file_extension))

# 轮询读取文件
def poll_files():
    last_files = set()
    # 模型路径
    model_path = "best.onnx"
    # 实例化模型
    model = YOLOv8Seg(model_path)
    conf = 0.35
    iou = 0.45

    current_files = set(get_files())
    # 找出新添加的文件
    new_files = current_files - last_files
    for file in new_files:
        print(f"Found new file: {file}")
        # 处理新文件
        process_file(file,model,conf,iou)
    # 更新文件列表
    last_files = current_files


# 处理文件的示例函数
def process_file(file,model,conf,iou):
    img = cv2.imread(file)
    starTime = time.time()
    depthimg = cv2.imread('depth.png', cv2.IMREAD_UNCHANGED)
    depth_calib_intr = [1044.018799,0,663.191772,
                        0,1044.018799,494.933533,
                        0,0,1]
    # 推理
    boxes, segments, _ = model(img, conf_threshold=0.35, iou_threshold=0.45)
    # 画图
    if len(boxes) > 0:
        output_image, result = model.draw_and_visualize(img, boxes, segments, depthimg, depth_calib_intr, vis=False, save=True)
    else:
        output_image = img
    print("图片完成检测")
    useTime = time.time() - starTime
    useTime = round(useTime, 2)
    textTime = f"useTime: {useTime} seconds"
    cv2.putText(output_image, textTime, (int(10), int(30)), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
    cv2.imshow("seg", output_image)
    cv2.imwrite('image_seg.jpg', output_image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

if __name__ == '__main__':
    poll_files()

    # # 模型路径
    # model_path = "best.onnx"
    # # 实例化模型
    # model = YOLOv8Seg(model_path)
    # conf = 0.35
    # iou = 0.45
    # # 三种模式 1为图片预测，并显示结果图片；2为摄像头检测，并实时显示FPS
    # mode = 1
    # if mode == 1:
    #     # opencv 读取图片

    #     img = cv2.imread('street.jpg')
    #     # 推理
    #     boxes, segments, _ = model(img, conf_threshold=conf, iou_threshold=iou)
    #     # 画图
    #     if len(boxes) > 0:
    #         output_image = model.draw_and_visualize(img, boxes, segments, vis=False, save=True)
    #     else:
    #         output_image = img
    #     print("图片完成检测")
    #     cv2.imshow("seg", output_image)
    #     cv2.imwrite('image_seg.jpg', output_image)
    #     cv2.waitKey(0)
    #     cv2.destroyAllWindows()
    # elif mode == 2:
    #     # 摄像头图像分割
    #     cap = cv2.VideoCapture(0)
    #     # 返回当前时间
    #     start_time = time.time()
    #     counter = 0
    #     while True:
    #         # 从摄像头中读取一帧图像
    #         ret, frame = cap.read()
    #         # 推理
    #         boxes, segments, _ = model(frame, conf_threshold=conf, iou_threshold=iou)
    #         # 画图
    #         if len(boxes) > 0:
    #             output_image = model.draw_and_visualize(frame, boxes, segments, vis=False, save=True)
    #         else:
    #             output_image = frame
    #         counter += 1  # 计算帧数
    #         # 实时显示帧数
    #         if (time.time() - start_time) != 0:
    #             cv2.putText(output_image, "FPS:{0}".format(float('%.1f' % (counter / (time.time() - start_time)))), (5, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.75, (255, 255, 255), 1)
    #             # 显示图像
    #             cv2.imshow('seg', output_image)
    #         if cv2.waitKey(1) & 0xFF == ord('q'):
    #             break
    #     # 释放资源
    #     cap.release()
    #     cv2.destroyAllWindows()
    # elif mode == 3:
    #     # 输入视频路径
    #     input_video_path = 'kun.mp4'
    #     # 输出视频路径
    #     output_video_path = 'kun_seg.mp4'
    #     # 打开视频文件
    #     cap = cv2.VideoCapture(input_video_path)
    #     # 检查视频是否成功打开
    #     if not cap.isOpened():
    #         print("Error: Could not open video.")
    #         exit()
    #     # 读取视频的基本信息
    #     frame_width = int(cap.get(3))
    #     frame_height = int(cap.get(4))
    #     fps = cap.get(cv2.CAP_PROP_FPS)
    #     # 定义视频编码器和创建VideoWriter对象
    #     fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # 根据文件名后缀使用合适的编码器
    #     out = cv2.VideoWriter(output_video_path, fourcc, fps, (frame_width, frame_height))
    #     # 初始化帧数计数器和起始时间
    #     frame_count = 0
    #     start_time = time.time()
    #     while True:
    #         ret, frame = cap.read()
    #         if not ret:
    #             print("Info: End of video file.")
    #             break
    #         # 推理
    #         boxes, segments, _ = model(frame, conf_threshold=conf, iou_threshold=iou)
    #         # 画图
    #         if len(boxes) > 0:
    #             output_image = model.draw_and_visualize(frame, boxes, segments, vis=False, save=True)这里
    #         else:
    #             output_image = frame
    #         # 计算并打印帧速率
    #         frame_count += 1
    #         end_time = time.time()
    #         elapsed_time = end_time - start_time
    #         if elapsed_time > 0:
    #             fps = frame_count / elapsed_time
    #             print(f"FPS: {fps:.2f}")
    #         # 将处理后的帧写入输出视频
    #         out.write(output_image)
    #         # （可选）实时显示处理后的视频帧
    #         cv2.imshow("Output Video", output_image)
    #         if cv2.waitKey(1) & 0xFF == ord('q'):
    #             break
    #     # 释放资源
    #     cap.release()
    #     out.release()
    #     cv2.destroyAllWindows()
    # else:
    #     print("输入错误，请检查mode的赋值")
