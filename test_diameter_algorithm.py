#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布捆直径检测算法测试脚本
测试新增的辅助判断线功能
"""

import cv2
import numpy as np
import os
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from YoloOnnx import YOLOv8Seg
    import config
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保YoloOnnx.py和config.py文件存在")
    sys.exit(1)

def test_diameter_algorithm():
    """
    测试布捆直径检测算法
    """
    print("=== 布捆直径检测算法测试 ===")
    
    # 检查模型文件
    model_path = "best.onnx"
    if not os.path.exists(model_path):
        print(f"错误: 模型文件 {model_path} 不存在")
        print("请确保模型文件在当前目录下")
        return False
    
    # 初始化模型
    try:
        model = YOLOv8Seg(model_path)
        print(f"✓ 成功加载模型: {model_path}")
    except Exception as e:
        print(f"✗ 加载模型失败: {e}")
        return False
    
    # 测试图片目录
    test_image_dir = "test_images"
    if not os.path.exists(test_image_dir):
        print(f"警告: 测试图片目录 {test_image_dir} 不存在")
        print("将使用当前目录下的图片文件")
        test_image_dir = "."
    
    # 查找测试图片
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    test_images = []
    
    for ext in image_extensions:
        for file in os.listdir(test_image_dir):
            if file.lower().endswith(ext) and 'ori' in file.lower():
                test_images.append(os.path.join(test_image_dir, file))
    
    if not test_images:
        print("未找到测试图片，请确保有包含'ori'的图片文件")
        return False
    
    print(f"找到 {len(test_images)} 张测试图片")
    
    # 模拟相机内参（根据实际相机调整）
    depth_calib_intr = [1853.0103759765625, 0, 1257.3001708984375, 
                       0, 1852.953369140625, 939.3478393554688, 
                       0, 0, 1]
    
    # 处理每张图片
    for i, img_path in enumerate(test_images[:3]):  # 只测试前3张图片
        print(f"\n--- 测试图片 {i+1}: {os.path.basename(img_path)} ---")
        
        # 读取图片
        img = cv2.imdecode(np.fromfile(img_path, dtype=np.uint8), cv2.IMREAD_COLOR)
        if img is None:
            print(f"✗ 无法读取图片: {img_path}")
            continue
        
        print(f"✓ 图片尺寸: {img.shape}")
        
        # 查找对应的深度图
        depth_path = img_path.replace('_ori.jpg', '_depth.png').replace('.ori.jpg', '_depth.png')
        if os.path.exists(depth_path):
            depthimg = cv2.imdecode(np.fromfile(depth_path, dtype=np.uint8), cv2.IMREAD_UNCHANGED)
            print(f"✓ 找到深度图: {os.path.basename(depth_path)}")
        else:
            # 创建模拟深度图
            depthimg = np.full((img.shape[0], img.shape[1]), 2500, dtype=np.uint16)
            print("⚠ 未找到深度图，使用模拟深度图")
        
        try:
            # YOLO检测
            print("开始YOLO检测...")
            boxes, segments, _ = model(img, conf_threshold=0.35, iou_threshold=0.45)
            print(f"✓ 检测到 {len(boxes)} 个目标")
            
            if len(boxes) > 0:
                # 应用改进的直径检测算法
                print("应用改进的直径检测算法...")
                output_image, results = model.draw_and_visualize(
                    img, boxes, segments, depthimg, depth_calib_intr, 
                    vis=False, save=False
                )
                
                if results:
                    print(f"✓ 成功处理 {len(results)} 个布捆")
                    
                    # 分析结果
                    for j, result in enumerate(results):
                        x, y, z, angle, px, py, diameter, length = result
                        print(f"  布捆 {j+1}: 位置=({x},{y},{z}), 直径={diameter}mm, 长度={length}mm")
                    
                    # 保存结果图片
                    output_path = f"test_result_{i+1}_{os.path.basename(img_path)}"
                    success, buffer = cv2.imencode('.jpg', output_image)
                    if success:
                        with open(output_path, 'wb') as f:
                            f.write(buffer)
                        print(f"✓ 保存结果图片: {output_path}")
                    else:
                        print("✗ 保存结果图片失败")
                else:
                    print("✗ 处理结果为空")
            else:
                print("⚠ 未检测到目标")
                
        except Exception as e:
            print(f"✗ 处理图片时发生错误: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n=== 测试完成 ===")
    print("请检查生成的结果图片，观察以下特征：")
    print("1. 红色/蓝色垂线：直径测量线")
    print("2. 黄色线：布捆两端连线（仅在偏差>20mm时显示）")
    print("3. 绿色虚线：中心点到两端连线的垂线（仅在偏差>20mm时显示）")
    print("4. 黄色文字：偏差数值（仅在偏差>20mm时显示）")
    print("5. 警告标记：⚠ DIAMETER CHECK（仅在偏差>20mm时显示）")
    
    return True

def create_test_config():
    """
    创建测试用的config模块（如果不存在）
    """
    config_content = '''
# 测试用配置文件
import logging

# 创建日志记录器
class Logger:
    def __init__(self):
        self.logger = logging.getLogger('test')
        self.logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

log = Logger()

# 模拟box_model（如果需要）
box_model = None
'''
    
    if not os.path.exists('config.py'):
        with open('config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✓ 创建测试配置文件: config.py")

if __name__ == "__main__":
    # 创建测试配置
    create_test_config()
    
    # 运行测试
    success = test_diameter_algorithm()
    
    if success:
        print("\n测试脚本执行完成！")
    else:
        print("\n测试脚本执行失败！")
        sys.exit(1)
