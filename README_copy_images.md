# PNG和ORI.JPG图片复制工具

这个工具可以帮助你复制文件夹中的PNG图片和对应的ORI.JPG图片到另一个文件夹。

## 功能特点

- 🔍 **智能匹配**: 自动查找PNG图片对应的ORI.JPG文件
- 📁 **递归搜索**: 支持在子目录中查找图片
- 🎯 **多种命名模式**: 支持多种ORI文件的命名方式
- 📊 **详细报告**: 显示复制进度和结果统计
- 🛡️ **错误处理**: 安全的文件复制操作

## 文件说明

### 1. `copy_images.py` - 命令行版本
支持命令行参数，适合批处理和自动化脚本。

**使用方法:**
```bash
python copy_images.py <源目录> <目标目录> [--flat]
```

**参数说明:**
- `源目录`: 包含PNG和ORI.JPG文件的源文件夹路径
- `目标目录`: 复制文件的目标文件夹路径
- `--flat`: 可选参数，平铺复制（不保持目录结构）

**示例:**
```bash
# 基本使用
python copy_images.py ./data ./output

# 平铺复制
python copy_images.py ./data ./output --flat
```

### 2. `copy_images_simple.py` - 交互式版本
提供友好的交互界面，适合手动操作。

**使用方法:**
```bash
python copy_images_simple.py
```

运行后会提示输入源目录和目标目录路径。

## 支持的ORI文件命名模式

脚本会自动查找以下命名模式的ORI文件：

1. `{base_name}_ori.jpg` - 例如: `image_ori.jpg`
2. `{base_name}_ori.JPG` - 例如: `image_ori.JPG`
3. `{base_name}.ori.jpg` - 例如: `image.ori.jpg`
4. `{base_name}.ori.JPG` - 例如: `image.ori.JPG`
5. `{base_name}_original.jpg` - 例如: `image_original.jpg`
6. `{base_name}_original.JPG` - 例如: `image_original.JPG`

其中 `{base_name}` 是PNG文件的基础名称（不包含扩展名）。

## 使用示例

假设你有以下文件结构：
```
data/
├── 20250708/
│   ├── image1.png
│   ├── image1_ori.jpg
│   ├── image2.png
│   └── image2_ori.jpg
└── 20250709/
    ├── image3.png
    └── image3_ori.jpg
```

运行脚本后，目标目录将包含：
```
output/
├── image1.png
├── image1_ori.jpg
├── image2.png
├── image2_ori.jpg
├── image3.png
└── image3_ori.jpg
```

## 输出信息

脚本会显示以下信息：

- 找到的PNG文件数量
- 每个PNG文件对应的ORI文件匹配情况
- 复制进度
- 最终统计结果（成功/失败数量）

## 注意事项

1. **文件覆盖**: 如果目标目录中已存在同名文件，会被覆盖
2. **权限要求**: 确保对源目录有读取权限，对目标目录有写入权限
3. **磁盘空间**: 确保目标磁盘有足够的空间存储复制的文件
4. **文件完整性**: 脚本会保持原始文件的修改时间和权限信息

## 错误处理

脚本会处理以下常见错误：
- 源目录不存在
- 目标目录创建失败
- 文件复制权限不足
- 磁盘空间不足
- 文件被其他程序占用

## 系统要求

- Python 3.6+
- 标准库模块：`os`, `shutil`, `pathlib`, `glob`, `argparse`

## 许可证

此脚本为开源工具，可自由使用和修改。 