import pcammls
from pcammls import * 
import cv2
import numpy
import sys
import os
import datetime
import time
import csv
import threading
from threading import Thread ,Timer
import logging
import config
#import pupil_apriltags as apriltag
import random
import math
import numpy as np
import queue
import YoloOnnx
import copy
import open3d as o3d

QRCODE = "CV2"

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

'''
托盘尺寸： 外 200*140 内 192*136

'''


# 定义一个继承自pcammls.DeviceEvent的类
class PythonPercipioDeviceEvent(pcammls.DeviceEvent):
    # 定义一个Offline属性，默认为False
    Offline = False

    # 初始化函数
    def __init__(self):
        pcammls.DeviceEvent.__init__(self)

    # 运行函数，当设备离线时，打印提示信息，并将Offline属性设置为True
    def run(self, handle, eventID):
        if eventID==TY_EVENT_DEVICE_OFFLINE:
            print('=== Event Callback: Device Offline!')
            self.Offline = True
        return 0

    # 返回Offline属性的值
    def IsOffline(self):
        return self.Offline
    
class CameraHandler:
    def __init__(self, sn, id, result_queue):
        self.sn = sn
        self.id = id
        self.cl = PercipioSDK()
        self.handle = self.cl.Open(sn)
        self.offline = False
        self.result_queue = result_queue
        self.isbusy = False
        self.task = None
        self.start = False
        self.msg = None
        self.depth_calib_intr = None
        self.img_w = 2560
        self.img_h = 1920
        self.start_time = time.time()
        self.result_list = []
        self.frame_count = 0
        self.MAX_FRAME_COUNT = 10
        self.image_id = 0
        self.pallet_center = {"x":0, "y":0, "z":0, "angle":0, "l":1820, "w":1160, "h":4300}

        if self.id =="DC1":
            self.pallet_center = {"x":400, "y":0, "z":2230, "angle":0, "l":1800, "w":1240, "h":3990}#{"x":0, "y":0, "z":2250, "angle":0, "l":1820, "w":1160, "h":3990}
        elif self.id =="DC2":
            self.pallet_center = {"x":160, "y":130, "z":2230, "angle":0, "l":1800, "w":1240, "h":3990}
        elif self.id =="DC3":
            self.pallet_center = {"x":-10, "y":-80, "z":2230, "angle":0, "l":1800, "w":1240, "h":3990}
        self.xyz_range = (self.pallet_center['x'] - self.pallet_center['l'] / 2 - 300, self.pallet_center['x'] + self.pallet_center['l'] / 2 + 300,
                          self.pallet_center['y'] - self.pallet_center['w'] / 2 - 150, self.pallet_center['y'] + self.pallet_center['w'] / 2 + 150,
                          self.pallet_center['z'] - 150, self.pallet_center['h'] + 150)
        if QRCODE == "CV2":
            self.detector = cv2.aruco.getPredefinedDictionary(cv2.aruco.DICT_APRILTAG_36H11)
        else:
            self.detector = apriltag.Detector(families='tag36h11')
        
        if not self.cl.isValidHandle(self.handle):
            err = self.cl.TYGetLastErrorCodedescription()
            print('No device found:', err)
            #sys.exit(1)
            self.offline = True
        else:
            self.event = PythonPercipioDeviceEvent()
            self.cl.DeviceRegiststerCallBackEvent(self.event)
            self.init_camera_settings()

            roi = PercipioAecROI(238*2, 175*2, 1140*2, 795*2)
            param =self.cl.DevParamFromPercipioAecROI(roi)
            self.cl.DeviceSetParameter(self.handle, TY_COMPONENT_RGB_CAM, TY_STRUCT_AEC_ROI, param)
            read_param = self.cl.DeviceGetParameter(self.handle, TY_COMPONENT_RGB_CAM, TY_STRUCT_AEC_ROI)
            m_read_param=read_param.toArray()
            print(f"m_read_param = {m_read_param}")

    def load_default_params(self):
        depth_fmt_list = self.cl.DeviceStreamFormatList(self.handle, PERCIPIO_STREAM_DEPTH)
        logging.info("Depth format list: %s", depth_fmt_list)
        # 配置深度图像格式
        self.cl.DeviceStreamFormatConfig(self.handle, PERCIPIO_STREAM_DEPTH, depth_fmt_list[1])
        # 加载默认参数
        err = self.cl.DeviceLoadDefaultParameters(self.handle)
        if err:
            logging.error('Load default parameters fail: %s', self.cl.TYGetLastErrorCodedescription())
        else:
            logging.info('Load default parameters successful')

        # 读取深度图像的标定尺度单位
        self.scale_unit = self.cl.DeviceReadCalibDepthScaleUnit(self.handle)
        logging.info('depth image scale unit :%s', self.scale_unit)
        # 读取深度图像和颜色图像的标定数据
        self.depth_calib = self.cl.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_DEPTH)
        self.color_calib = self.cl.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_COLOR)

    def depth2xyz(self, px, py, depth):
        cx = self.depth_calib_intr[2]#663.191772
        cy = self.depth_calib_intr[5]#494.933533
        fx = self.depth_calib_intr[0]#1044.018799
        fy = self.depth_calib_intr[4]#1044.018799
        z = int(depth)
        x = int((px - cx) * z / fx)
        y = int((py - cy) * z / fy)
        result = [x, y, z]
        return result

    def xyz2depth(self, x, y, z):
        cx = self.depth_calib_intr[2]
        cy = self.depth_calib_intr[5]
        fx = self.depth_calib_intr[0]
        fy = self.depth_calib_intr[4]
        px = int(x * fx / z + cx)
        py = int(y * fy / z + cy)
        result = [px, py]
        return result

    def init_camera_settings(self,color_fmt_sw = 0 ):
        # 获取颜色图像格式列表
        color_fmt_list = self.cl.DeviceStreamFormatDump(self.handle, PERCIPIO_STREAM_COLOR)
        if len(color_fmt_list) == 0:
            print ('device has no color stream.')
            return
        # 打印颜色图像格式列表
        print ('color image format list:')
        for idx in range(len(color_fmt_list)):
            fmt = color_fmt_list[idx]
            print ('\t{} -size[{}x{}]\t-\t desc:{}'.format(idx, self.cl.Width(fmt), self.cl.Height(fmt), fmt.getDesc()))
        """
            color image format list:
                0 -size[2560x1920]      -        desc:yuyv 2560x1920
                1 -size[1920x1440]      -        desc:yuyv 1920x1440
                2 -size[1280x960]       -        desc:yuyv 1280x960
                3 -size[640x480]        -        desc:yuyv 640x480
                4 -size[2560x1920]      -        desc:CSI BAYER12GR_2560x1920
        """
        # 配置颜色图像格式
        self.cl.DeviceStreamFormatConfig(self.handle, PERCIPIO_STREAM_COLOR, color_fmt_list[color_fmt_sw])

        # 获取深度图像格式列表
        depth_fmt_list = self.cl.DeviceStreamFormatDump(self.handle, PERCIPIO_STREAM_DEPTH)
        # 如果深度图像格式列表为空，打印提示信息并返回
        if len(depth_fmt_list) == 0:
            print ('device has no depth stream.')
            return

        # 打印深度图像格式列表
        print ('depth image format list:')
        for idx in range(len(depth_fmt_list)):
            fmt = depth_fmt_list[idx]
            print ('\t{} -size[{}x{}]\t-\t desc:{}'.format(idx, self.cl.Width(fmt), self.cl.Height(fmt), fmt.getDesc()))
        """
            depth image format list:
                0 -size[640x480]        -        desc:DEPTH16_640x480
                1 -size[1280x960]       -        desc:DEPTH16_1280x960
                2 -size[320x240]        -        desc:DEPTH16_320x240
        """
        # 配置深度图像格式
        self.cl.DeviceStreamFormatConfig(self.handle, PERCIPIO_STREAM_DEPTH, depth_fmt_list[1])
        # 加载默认参数
        err = self.cl.DeviceLoadDefaultParameters(self.handle)
        # 如果加载失败，打印错误信息
        if err:
            logging.error('Load default parameters fail: %s', self.cl.TYGetLastErrorCodedescription())
        else:
            logging.info('Load default parameters successful')
        
        # 读取深度图像的标定尺度单位
        self.scale_unit = self.cl.DeviceReadCalibDepthScaleUnit(self.handle)
        print ('depth image scale unit :{}'.format(self.scale_unit))
        # 读取深度图像和颜色图像的标定数据
        self.depth_calib = self.cl.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_DEPTH)
        self.color_calib = self.cl.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_COLOR)
        depth_calib_width  = self.depth_calib.Width()
        depth_calib_height = self.depth_calib.Height()
        #self.img_w = depth_calib_width
        #self.img_h = depth_calib_height
        self.depth_calib_intr   = self.depth_calib.Intrinsic()
        depth_calib_extr   = self.depth_calib.Extrinsic()
        depth_calib_dis    = self.depth_calib.Distortion()
        print('delth calib info:')
        print('\tcalib size       :[{}x{}]'.format(depth_calib_width, depth_calib_height))
        print('\tcalib intr       : {}'.format(self.depth_calib_intr))
        print('\tcalib extr       : {}'.format(depth_calib_extr))
        print('\tcalib distortion : {}'.format(depth_calib_dis))
        color_calib_width  = self.color_calib.Width()
        color_calib_height = self.color_calib.Height()
        color_calib_intr   = self.color_calib.Intrinsic()
        color_calib_extr   = self.color_calib.Extrinsic()
        color_calib_dis    = self.color_calib.Distortion()
        self.img_w = color_calib_width
        self.img_h = color_calib_height
        self.depth_calib_intr   = self.color_calib.Intrinsic()
        print('color calib info:')
        print('\tcalib size       :[{}x{}]'.format(color_calib_width, color_calib_height))
        print('\tcalib intr       : {}'.format(color_calib_intr))
        print('\tcalib extr       : {}'.format(color_calib_extr))
        print('\tcalib distortion : {}'.format(color_calib_dis))
        pass 

    def start_stream(self):
        try:
            err = self.cl.DeviceStreamEnable(self.handle, PERCIPIO_STREAM_COLOR | PERCIPIO_STREAM_DEPTH)
            if err:
                logging.error('device stream enable err: %s', err)
                return False
            self.cl.DeviceStreamOn(self.handle)
            return True
        except Exception as e:
            logging.error("Error starting stream: %s", e)
            return False

    def get_images(self):
        try:
            if self.event.IsOffline():
                return None
            image_list = self.cl.DeviceStreamRead(self.handle, 5000)
            if len(image_list) == 2:
                return image_list
            return None
        except Exception as e:
            logging.error("Error getting images: %s", e)
            return None
    def resize_img(self, img, window_name,scale_factor = 0.25):           # 计算新的尺寸
            new_width = int(img.shape[1] * scale_factor)
            new_height = int(img.shape[0] * scale_factor)
            # 缩放图片
            resized_img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)
            cv2.imshow(window_name, resized_img)
            cv2.waitKey(1)
    def process_images(self, image_list):
        try:
            for i in range(len(image_list)):
                frame = image_list[i]
                if frame.streamID == PERCIPIO_STREAM_DEPTH:
                    img_depth = frame
                if frame.streamID == PERCIPIO_STREAM_COLOR:
                    img_color = frame

            if img_depth is None or img_color is None:
                logging.warning("Missing depth or color image")            
                return None, None, None

            img_registration_depth = image_data()
            img_registration_render = image_data()
            img_parsed_color = image_data()
            img_undistortion_color = image_data()

            self.cl.DeviceStreamMapDepthImageToColorCoordinate(self.depth_calib, img_depth.width, img_depth.height, self.scale_unit, img_depth, self.color_calib, img_color.width, img_color.height, img_registration_depth)
            self.cl.DeviceStreamDepthRender(img_registration_depth, img_registration_render)
            np_img_registration_depth = img_registration_depth.as_nparray()
            mat_depth_render = img_registration_render.as_nparray()

            self.cl.DeviceStreamImageDecode(img_color, img_parsed_color)
            self.cl.DeviceStreamDoUndistortion(self.color_calib, img_parsed_color, img_undistortion_color)
            mat_undistortion_color = img_undistortion_color.as_nparray()

            if mat_undistortion_color.shape[:2] != mat_depth_render.shape[:2]:
                mat_depth_render = cv2.resize(mat_depth_render, (mat_undistortion_color.shape[1], mat_undistortion_color.shape[0]))

            if len(mat_undistortion_color.shape) == 2:
                mat_undistortion_color = cv2.cvtColor(mat_undistortion_color, cv2.COLOR_GRAY2BGR)
                mat_overlay = cv2.addWeighted(mat_undistortion_color, 0.5, mat_depth_render, 0.5, 0)
            elif len(mat_depth_render.shape) == 2:
                mat_depth_render_rgb = cv2.cvtColor(mat_depth_render, cv2.COLOR_GRAY2BGR)
                mat_overlay = cv2.addWeighted(mat_undistortion_color, 0.5, mat_depth_render_rgb, 0.5, 0)
            else:
                mat_overlay = cv2.addWeighted(mat_undistortion_color, 0.5, mat_depth_render, 0.5, 0)
            self.resize_img(mat_overlay, 'overlay-' + self.id)
            draw_img = copy.deepcopy(mat_undistortion_color)
            tl_pt, br_pt = self.get_box_draw_rect(self.pallet_center["x"], self.pallet_center["y"], self.pallet_center["z"], 2000, 1400)
            cv2.rectangle(draw_img,(tl_pt[0], tl_pt[1]),(br_pt[0], br_pt[1]), (0, 0, 125), 2)
            self.resize_img(draw_img, 'mat_undistortion_color-' + self.id)
            if self.start:
                now = time.time()
                if abs(now - self.start_time) > 10:
                    self.msg["data"]["result"] = "fail"
                    self.msg["data"]["codelist"] = [1]
                    self.msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                    self.result_queue.put(self.msg)
                    self.start = False
                    self.isbusy = False
                elif np_img_registration_depth is not None and mat_undistortion_color is not None and mat_overlay is not None:
                    self.frame_count += 1
                    if self.frame_count >= self.MAX_FRAME_COUNT:
                        self.msg["data"]["result"] = "fail"
                        self.msg["data"]["codelist"] = [1]
                        self.msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                        self.result_queue.put(self.msg)
                        self.start = False
                        self.isbusy = False
                    else:
                        save_img = copy.deepcopy(mat_undistortion_color)
                        ret,result = self.get_img_result(np_img_registration_depth,mat_undistortion_color)
                        if ret:
                            self.result_list.append(result) 
                            if len(self.result_list) >= 2:
                                ret, result_msg = self.result_processing(self.result_list,mat_undistortion_color)
                                if ret:
                                    self.image_id += 1
                                    self.save_csv(mat_undistortion_color, result_msg, self.image_id, self.start_time,img_ori = save_img,img_depth = np_img_registration_depth)
                                    result_msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                                    self.result_queue.put(result_msg)
                                    self.start = False
                                    self.isbusy = False
                                    print(f"----------------------")
                                    print(f"----------------------")
                                    print(f"第{self.image_id}次测试")
                                    print(f"----------------------")
                                    print(f"----------------------")
                                    if self.image_id >= 10:
                                        self.image_id = 0
                                else:
                                    self.msg["data"]["result"] = "fail"
                                    self.msg["data"]["codelist"] = [1]
                                    self.msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                                    self.result_queue.put(self.msg)
                                    self.start = False
                                    self.isbusy = False
        except Exception as e:
            logging.error("Error processing images: %s", e)
            return None, None, None

    def stop_stream(self):
        try:
            self.cl.DeviceStreamOff(self.handle)
        except Exception as e:
            config.log.logger.error("Error stopping stream: %s", e)

    def close_camera(self):
        try:
            self.cl.Close(self.handle)
        except Exception as e:
            config.log.logger.error("Error closing camera: %s", e)

    def run_task(self, msg):
        if self.isbusy:
            config.log.logger.warning("Camera is busy, cannot run task")
            return False
        else:
            ret, self.msg = self.get_msg(msg)
            if ret:
                self.start_time = time.time()
                self.result_list = []
                self.frame_count = 0
                self.isbusy = True
                self.start = True
                self.task = msg
                return ret
            else:
                return False

    def get_msg(self, msg):
        try:
            result_msg = {} 
            result_msg["eid"] = msg["eid"]
            result_msg["dev"] = msg["dev"]
            result_msg["type"] = msg["type"] + "Feedback"
            result_msg["taskNum"] = msg["taskNum"]
            result_msg["sendTime"] = msg["sendTime"]
            result_msg["data"] = {}
            if msg["type"] == "selfCheck":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["codelist"] = [0]
            elif msg["type"] == "box":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["points"] = []
            elif msg["type"] == "palletCenter":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["center"] = [0,0,0]
                result_msg["data"]["angle"] = 0
            elif msg["type"] == "placementLocation":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["points"] = [] #xyzw
                
            elif msg["type"] == "beltBox":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["box"] = "existence"
                result_msg["data"]["point"] = [] #xyzw
            elif msg["type"] == "QRCode":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["coordinates"] = [] #id x y z px py

            else:
                return False,None
            return True,result_msg
        except Exception as e:
            config.log.logger.error("Error getting message: %s", e)
            return False,None

    def result_processing(self, result_list, img):
        try:
            if self.task["type"] == "selfCheck":
                self.msg["data"]["result"] = "pass"
                return True, self.msg
            if self.task["type"] == "box":
                max_result = max(result_list, key=lambda x: x[0])
                self.msg["data"]["points"] = max_result
                print("box",self.msg["data"]["points"])
                self.msg["data"]["result"] = "pass"
                self.msg["data"]["code"] = [0]
                
            elif self.task["type"] == "palletCenter":
                self.msg["data"]["result"] = "pass"
                self.msg["data"]["code"] = [0]
                pallet_l = 2000
                pallet_w = 1400
                lt = [self.pallet_center["x"] - pallet_l/2, self.pallet_center["y"] - pallet_w / 2, self.pallet_center["z"]]
                rt = [self.pallet_center["x"] + pallet_l/2, self.pallet_center["y"] - pallet_w / 2, self.pallet_center["z"]]
                lb = [self.pallet_center["x"] - pallet_l/2, self.pallet_center["y"] + pallet_w / 2, self.pallet_center["z"]]
                rb = [self.pallet_center["x"] + pallet_l/2, self.pallet_center["y"] + pallet_w / 2, self.pallet_center["z"]]
                center = [self.pallet_center["x"], self.pallet_center["y"], self.pallet_center["z"]]
                self.msg["data"]["center"] = [lt,rt,lb,rb,center]
                self.msg["data"]["angle"] = 0
                
            elif self.task["type"] == "placementLocation":
                self.msg["data"]["result"] = "pass"
                self.msg["data"]["code"] = [0]
                self.msg["data"]["points"] = [] #xyzw
                self.msg["data"]["minH"] = self.pallet_center['h'] - self.task["task"]["height"]

                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 1
                color = (0, 246, 26)
                thickness = 2
                # 定义文本行距
                line_spacing = 20

                first_grid = result_list[0]
                for grid in range(1, len(result_list)):
                    for i, element in enumerate(result_list[grid]):
                        first_grid[i]["ptcnt"] += element["ptcnt"]
                pts = []
                for grid in first_grid:
                    grid["ptcnt"] /= len(result_list)
                    if grid['ptcnt'] <= (150 * len(result_list)):
                        cv2.rectangle(img,(grid['tl_pt'][0], grid['tl_pt'][1]),(grid['br_pt'][0], grid['br_pt'][1]), (0, 0, 255), 2)
                    else:
                        cv2.rectangle(img,(grid['tl_pt'][0], grid['tl_pt'][1]),
                            (grid['br_pt'][0], grid['br_pt'][1]), (125, 125, 0), 2)
                        cv2.putText(img, f"x:{grid['x']}", (int(grid['tl_pt'][0])+40, int(grid['tl_pt'][1])+40), font, font_scale, color, thickness)
                        cv2.putText(img, f"y:{grid['y']}", (int(grid['tl_pt'][0])+40, int(grid['tl_pt'][1])+80), font, font_scale, color, thickness)
                        cv2.putText(img, f"z:{grid['z']}", (int(grid['tl_pt'][0])+40, int(grid['tl_pt'][1])+120), font, font_scale, color, thickness)
                        pt = [grid["x"], grid["y"], grid["z"], 0]
                        pts.append(pt)
                z_values = [pt[2] for pt in pts] 
                z_max = max(z_values)
                z_min = min(z_values)
                if abs(z_min - z_max) >=  self.task["task"]["height"] * 2:
                    self.msg["data"]["result"] = "fail"
                    self.msg["data"]["code"] = [1]#高度异常

                z_max = int(z_max - self.task["task"]["height"] * 0.6)
                points = []
                for pt in pts:
                    if pt[2] >= z_max:
                        points.append(pt)
                for grid in first_grid:
                    if grid['z'] >= z_max:
                        cv2.rectangle(img,(grid['tl_pt'][0], grid['tl_pt'][1]),
                                (grid['br_pt'][0], grid['br_pt'][1]), (255, 255, 0), 2)
                        cv2.putText(img, f"x:{grid['x']}", (int(grid['tl_pt'][0])+40, int(grid['tl_pt'][1])+40), font, font_scale, color, thickness)
                        cv2.putText(img, f"y:{grid['y']}", (int(grid['tl_pt'][0])+40, int(grid['tl_pt'][1])+80), font, font_scale, color, thickness)
                        cv2.putText(img, f"z:{grid['z']}", (int(grid['tl_pt'][0])+40, int(grid['tl_pt'][1])+120), font, font_scale, color, thickness)

                self.msg["data"]["points"] = points
                self.msg["data"]["minH"] = z_min
                self.resize_img(img, 'placementLocation-' + self.id)

            elif self.task["type"] == "beltBox":
                self.msg["data"]["result"] = "pass"
                self.msg["data"]["code"] = [0]
                self.msg["data"]["box"] = "existence"
                self.msg["data"]["point"] = [] #xyzw
                
            elif self.task["type"] == "QRCode":
                self.msg["data"]["result"] = "pass"
                self.msg["data"]["code"] = [0]
                ids = [result[0] for result in result_list]
                if len(set(ids)) != 1:
                    return False,None
                get_id = ids[0]
                # 使用 zip 和列表推导式求和
                sum_values = [sum(values) for values in zip(*result_list)][1:]  # 跳过第一个元素，因为它是 id
                # 计算平均值
                average_values = [int(val / len(result_list)) for val in sum_values]
                #sum_values = sum(result[1:] for result in result_list)  # 求和
                #average_values = [int(val / len(result_list)) for val in sum_values]
                self.msg["data"]["coordinates"] = [get_id] + average_values
                px = self.msg["data"]["coordinates"][4]
                py = self.msg["data"]["coordinates"][5]
                cv2.circle(img, (px, py), 3, (0, 255, 0), -1)
                cv2.putText(img, f"id:{get_id} x:{average_values[0]} y:{average_values[1]} z:{average_values[2]}", (px - 180, py + 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (126, 0, 0), 2)
                self.resize_img(img, 'QRCode-' + self.id)
                
                
            else:
                return False,None
            return True,self.msg
        except Exception as e:
            config.log.logger.error("Error processing results: %s", e)
            return False, None

    def get_img_result(self, np_depth, mat_color):
        ret = False
        result = None
        try:
            if self.task["type"] == "QRCode":
                ret, result = self.task_qrcode(np_depth,mat_color)
            elif self.task["type"] == "box":
                ret, result, mat_color = self.task_box(np_depth,mat_color)
                if ret and mat_color is not None:
                    self.resize_img(mat_color, 'box-' + self.id,scale_factor = 0.4)
                    
            elif self.task["type"] == "palletCenter":
                ret = True                
                result = self.pallet_center
            elif self.task["type"] == "placementLocation":
                ret, result = self.task_placementLocation(np_depth,mat_color)
            return ret,result
        except Exception as e:
            config.log.logger.error("Error processing images: %s", e)
            return False, None

    def task_qrcode(self, np_depth, mat_color): #二维码检测
        try:
            for i in range(4):
                alp = 0.5 + 0.3 * i
                imgA = cv2.convertScaleAbs(mat_color, alpha=alp)
                imgA = cv2.GaussianBlur(imgA, (3, 3), 0)
                grayA = cv2.cvtColor(imgA, cv2.COLOR_BGR2GRAY)
                if QRCODE == "CV2":
                    corners, ids, rejectedImgPoints = cv2.aruco.detectMarkers(grayA, self.detector)
                    if ids is not None:
                        for i, (corner, id) in enumerate(zip(corners, ids)):
                            # 计算中心坐标
                            center_x = int(sum(corner[0][:, 0]) / len(corner[0]))
                            center_y = int(sum(corner[0][:, 1]) / len(corner[0]))
                            (px, py) = (int(center_x), int(center_y))
                            # 输出ID和中心坐标
                            print(f"Tag {id[0]} center at: ({center_x}, {center_y})")
                            tag_id = id[0]
                            ret, result = self.get_code_depth(np_depth, px, py, 25, tag_id)
                            print("result",result)
                            if ret:
                                return True,result
                else:
                    results = self.detector.detect(grayA)
                    if results is not None:
                        for r in results:
                            pt = self.get_code_points(r)
                            (px, py) = (int(r.center[0]), int(r.center[1]))
                            tag_id = r.tag_id
                            ret, result = self.get_code_depth(np_depth, px, py, pt[2], tag_id)
                            print("result",result)
                            if ret:
                                return True,result
            return False, None
        except Exception as e:
            config.log.logger.error("Error task_qrcode: %s", e)
            return False, None
    def task_placementLocation(self, np_depth, mat_color): #放置位置检测
        try:

            #np_depth = cv2.imread('4_depth.png', cv2.IMREAD_UNCHANGED)
            #np_depth = np.asanyarray(np_depth)


            self.pinhole_camera_intrinsic = o3d.camera.PinholeCameraIntrinsic(
                        self.img_w, self.img_h, self.depth_calib_intr[0], self.depth_calib_intr[4], self.depth_calib_intr[2], self.depth_calib_intr[5])
            
            pallet_l = self.pallet_center["l"]  #长
            pallet_w = self.pallet_center["w"]  #宽
            pallet_h = self.pallet_center["h"]  #高
            pallet_center_x = self.pallet_center["x"]  #x
            pallet_center_y = self.pallet_center["y"]  #y
            box_l = self.task["task"]["length"]
            box_w = self.task["task"]["width"]
            box_h = self.task["task"]["height"]
            num_boxes_along_length = pallet_l // box_l
            num_boxes_along_width = pallet_w // box_w
            box_l_gap = int(pallet_l / num_boxes_along_length)
            box_w_gap = int(pallet_w / num_boxes_along_width)
            box_coordinates = []
            box_add = 10
            for i in range(num_boxes_along_length):
                for j in range(num_boxes_along_width):
                    # 计算纸箱中心的x坐标
                    box_x = pallet_center_x + (i - (num_boxes_along_length - 1) / 2) * (box_l_gap + box_add)
                    # 计算纸箱中心的y坐标
                    box_y = pallet_center_y + (j - (num_boxes_along_width - 1) / 2) * (box_w_gap + box_add)
                    draw_h = pallet_h# - box_h
                    tl_pt, br_pt = self.get_box_draw_rect(box_x, box_y, draw_h, box_l, box_w)
                    element = {'x':box_x, 'y':box_y, 'z':pallet_h, 'w':box_w, 'l':box_l, 'tl_pt':tl_pt, 'br_pt':br_pt, 'ptcnt':0}
                    box_coordinates.append(element)
                    
            if len(box_coordinates) > 0:
                
                img_depth = o3d.geometry.Image(np_depth)
                img_color = o3d.geometry.Image(mat_color)
                # #从彩色图、深度图创建RGBD
                # rgbd_image = o3d.geometry.RGBDImage.create_from_color_and_depth(
                #     img_color, img_depth, convert_rgb_to_intensity=False
                # )

                # # 相机外参，表示相机的位置和方向，通常是一个单位矩阵如果相机是静止不动的
                # extrinsic = np.eye(4)

                # # 创建点云
                # pcd = o3d.geometry.PointCloud.create_from_rgbd_image(
                #     rgbd_image,
                #     self.pinhole_camera_intrinsic,  # 使用内参矩阵
                #     extrinsic,  # 外参矩阵
                #     depth_trunc=5.0,  # 指定深度截断值，超过这个距离的点将不会被包含在点云中
                #     project_valid_depth_only=True  # 只投影有效的深度值
                # )
                rgbd = o3d.geometry.RGBDImage.create_from_color_and_depth(img_color, img_depth,depth_trunc=4.1)
                # 创建pcd
                pcd = o3d.geometry.PointCloud.create_from_rgbd_image(rgbd,self.pinhole_camera_intrinsic)#,depth_trunc=5.0
                #voxel_down_pcd = pcd.voxel_down_sample(voxel_size=0.01)
                #cl, ind = voxel_down_pcd.remove_radius_outlier(nb_points=16, radius=0.05)
                #pcd.points = cl.points
                points = np.asarray(pcd.points)
                #maxpt = 0
                #for pt in points:
                #    if maxpt < pt[2]:
                #        maxpt = pt[2]
                #o3d.visualization.draw_geometries([pcd])
                location_grid = []
                min_bound = [(pallet_center_x - pallet_l / 2 + 150) / 1000, (pallet_center_y - pallet_w / 2 + 150)/1000, pallet_h/1000 - 2.0]
                max_bound = [(pallet_center_x + pallet_l / 2 - 150) / 1000, (pallet_center_y + pallet_w / 2 - 150)/1000, pallet_h/1000 + 0.15]
                cropped_indice = np.where(((points >= min_bound) & (points <= max_bound)).all(axis=1))[0]
                pcd = pcd.select_by_index(cropped_indice)
                points = np.asarray(pcd.points)
                #显示
                #o3d.visualization.draw_geometries([cropped_point])
                #min_bounds = np.array([[element['x']/1000 - box_l/2/1000, element['y']/1000 - box_w/2/1000, element['z']/1000 - 2.5] for element in box_coordinates])
                #max_bounds = np.array([[element['x']/1000 + box_l/2/1000, element['y']/1000 + box_w/2/1000, element['z']/1000 - 0.15] for element in box_coordinates])

                #bounds = np.array([[element['x']/1000 - box_l/2/1000, element['x']/1000 + box_l/2/1000, 
                #                    element['y']/1000 - box_w/2/1000, element['y']/1000 + box_w/2/1000,
                #                    element['z']/1000 - 2.5], element['z']/1000 + 0.1] for element in box_coordinates])

                #for i, element in enumerate(box_coordinates):
                #    min_bound = min_bounds[i]
                #    max_bound = max_bounds[i]
                #    cropped_indices = np.where(((points >= min_bound) & (points <= max_bound)).all(axis=1))[0]
                #    cropped_point_cloud = pcd.select_by_index(cropped_indices)
                #    num_points_in_cropped_region = len(cropped_point_cloud.points)
                #    element['ptcnt'] += num_points_in_cropped_region
                #    location_grid.append(element)
                #    cropped_point_cloud = point_cloud.crop(bounds)

                min_bounds = np.array([[element['x']/1000 - box_l/2/1000, element['y']/1000 - box_w/2/1000, element['z']/1000 - 1.8] for element in box_coordinates])
                max_bounds = np.array([[element['x']/1000 + box_l/2/1000, element['y']/1000 + box_w/2/1000, element['z']/1000 + 0.15] for element in box_coordinates])

                #obb = sub_cloud.get_axis_aligned_bounding_box()
                #obb = sub_cloud.get_oriented_bounding_box()
                #obb.color = (1, 0, 0)
                obbs = []
                for i, element in enumerate(box_coordinates):
                    min_bound = min_bounds[i]
                    max_bound = max_bounds[i]
                    cropped_indices = np.where(((points >= min_bound) & (points <= max_bound)).all(axis=1))[0]
                    cropped_point_cloud = pcd.select_by_index(cropped_indices)
                    nb_neighbors = 15  # 指定邻居点的数量
                    std_ratio = 2.0  # 标准偏差比率
                    voxel_size = 0.03  # 体素大小，单位与点云数据单位一致
                    cleaned_pcd = cropped_point_cloud.voxel_down_sample(voxel_size)
                    cleaned_pcd, ind = cleaned_pcd.remove_statistical_outlier(nb_neighbors, std_ratio)
                    #cleaned_pcd, ind = cleaned_pcd.remove_radius_outlier(nb_points=30, radius=0.05)
                    if len(cleaned_pcd.points)<= 10:
                        continue
                    obb = cleaned_pcd.get_axis_aligned_bounding_box()
                    if obb is not None:
                        obb.color = (1, 0, 0)
                        obbs.append(obb)

                        num_points_in_cropped_region = len(cropped_point_cloud.points)
                        element['ptcnt'] += num_points_in_cropped_region

                        element['z'] = int(obb.min_bound[2] * 1000)
                        tl_pt, br_pt = self.get_box_draw_rect(element['x'], element['y'], element['z'], self.task["task"]["length"], self.task["task"]["width"])
                        element['tl_pt'] = tl_pt
                        element['br_pt'] = br_pt
                        
                        location_grid.append(element)
                    

                #o3d.visualization.draw_geometries([pcd] + obbs)


                return True, location_grid
            else:
                return False, None
        except Exception as e:
            config.log.logger.error("Error task_placementLocation: %s", e)
            return False, None
    def get_box_draw_rect(self, x, y, z, l, w):
        tl_pt = self.xyz2depth(x - l / 2, y - w / 2, z)
        br_pt = self.xyz2depth(x + l / 2, y + w / 2, z)
        return tl_pt, br_pt


    def task_box(self, np_depth, mat_color): #检测
        try:
            box_results = []
            #mat_color = cv2.imread("./test_1.png")
            output, results = self.task_box_process(mat_color, np_depth, config.box_model)
            if results is not None:                 
                #x,y,z,a,px,py,w,l
                minz = 4000
                for result in results:
                    x,y,z,a,px,py,w,l = result
                    if minz > z:
                        minz = z
                    w_mm, l_mm = self.get_box_wl(result)
                    box_results.append([x,y,z,a,l_mm,w_mm])

                # img_depth = o3d.geometry.Image(np_depth)
                # img_color = o3d.geometry.Image(mat_color)
                # rgbd = o3d.geometry.RGBDImage.create_from_color_and_depth(img_color, img_depth,depth_trunc=4.1)
                # self.pinhole_camera_intrinsic = o3d.camera.PinholeCameraIntrinsic(
                #             self.img_w, self.img_h, self.depth_calib_intr[0], self.depth_calib_intr[4], self.depth_calib_intr[2], self.depth_calib_intr[5])
            
                # pcd = o3d.geometry.PointCloud.create_from_rgbd_image(rgbd,self.pinhole_camera_intrinsic)
                # points = np.asarray(pcd.points)
                # pallet_l = self.pallet_center["l"]  #长
                # pallet_w = self.pallet_center["w"]  #宽
                # pallet_h = self.pallet_center["h"]  #高
                # pallet_center_x = self.pallet_center["x"]  #x
                # pallet_center_y = self.pallet_center["y"]  #y
                # min_bound = [(pallet_center_x - pallet_l / 2 + 150) / 1000, (pallet_center_y - pallet_w / 2 + 150)/1000, pallet_h/1000 - 2.0]
                # max_bound = [(pallet_center_x + pallet_l / 2 - 150) / 1000, (pallet_center_y + pallet_w / 2 - 150)/1000, minz/1000 - 0.10]
                # cropped_indice = np.where(((points >= min_bound) & (points <= max_bound)).all(axis=1))[0]
                # if len(cropped_indice) >= 150:
                #     len_pt = len(cropped_indice)
                #     config.log.logger.error(f"cropped_indice {len_pt}")
                #     return False, None, None
                return True,box_results,output
            else:
                return False, None, None
        except Exception as e:
            config.log.logger.error("Error task_box: %s", e)
            return False, None, None
    
    def get_box_wl(self, box):
        x,y,z,a,px,py,w,l = box
        pl = self.depth2xyz(-l/2, 0, z)
        pr = self.depth2xyz(l/2, 0, z)
        l_mm = int(self.pt_distance(pl, pr))
        w_mm = int(l_mm * w / l)
        return w_mm, l_mm
    
    def task_box_process(self, img, depthimg, model):
        try:
            starTime = time.time()
            # 推理
            boxes, segments, _ = model(img, conf_threshold=0.35, iou_threshold=0.45)
            # 画图
            
            if len(boxes) > 0:
                output_image, result = model.draw_and_visualize(img, boxes, segments, depthimg, self.depth_calib_intr, vis=False, save=True ,xyz_range = self.xyz_range)
            else:
                output_image = img
                result = None            
            print("图片完成检测")
            useTime = time.time() - starTime
            useTime = round(useTime, 2)
            textTime = f"useTime: {useTime} seconds - {self.image_id}"
            cv2.putText(output_image, textTime, (int(10), int(30)), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            # cv2.imshow("box"+self.id, output_image)
            # cv2.waitKey(1)
            return output_image, result

        except Exception as e:            
            config.log.logger.error("Error task_box_process: %s", e)
            return None, None

    def run_camera_in_main_thread(self):
        print('run_camera_in_main_thread:')
        try:
            if not self.start_stream():
                config.log.logger.error(f"Failed to start stream for camera")
                return
            while True:
                time.sleep(0.01)
                status =self.cl.DeviceGetParameter(self.handle, TY_COMPONENT_RGB_CAM, TY_BOOL_AUTO_EXPOSURE)
                m_status=status.toBool()
                
                image_list = self.get_images()

                if image_list:
                    self.process_images(image_list)
        finally:
            self.stop_stream()
            self.close_camera()

    def get_code_depth(self, depthImage,  cX, cY, minDistance, id):
        """
        根据深度图像和编码信息获取编码的深度信息。
        :param depthImage: 深度图像，用于获取编码区域的深度信息
        :param code: 编码信息，包含编码的位置和尺寸等信息
        :return: 返回一个元组，包含编码
        """
        try:
            distancesList = []
            # (cX, cY) = (int(code.center[0]), int(code.center[1]))
            # distances = [self.ptDistance((cX, cY), corner) for corner in code.corners]
            # minDistance = min(distances)
            randomPoints = self.generate_random_points(cX, cY, minDistance, 50)
            for (px,py) in randomPoints:
                dist = depthImage[py,px]
                if dist != 0 and dist < 4000 and dist > 350:
                    distancesList.append(dist)
            if len(distancesList) > 0:
                meanDistance = int(np.mean(distancesList))
                ptDepth = self.depth2xyz(cX, cY, meanDistance)
                return True, [id, int(ptDepth[0]), int(ptDepth[1]), int(ptDepth[2]), cX, cY]
        except:
            return False, None

    def get_code_points(self, code):
        """
        计算给定二维码的中心坐标和其到四个角的最短距离。
        :param code: 二维码对象，包含二维码的中心和四个角的信息。
        :return: 返回一个元组，包含二维码中心的X坐标、Y坐标以及中心到四个角的最短距离（单位为像素）。
        """
        (cX, cY) = (int(code.center[0]), int(code.center[1]))
        distances = [self.pt_distance((cX, cY), corner) for corner in code.corners]
        minDistance = min(distances)
        return cX, cY, int(minDistance)

    def generate_random_points(self, x, y, R, n):
        """
        生成在以(x, y)为圆心、半径为R的圆内的随机点。
        param:
        x, y: 圆心的坐标
        R: 圆的半径
        n: 需要生成的点的数量
        return:
        一个包含n个在圆内的随机点的列表，每个点都是一个(x, y)坐标元组。
        """
        points = []
        while len(points) < n:
            theta = random.uniform(0, 2 * np.pi)
            r = random.uniform(0, R)
            xp = x + r * np.cos(theta)
            yp = y + r * np.sin(theta)
            if 0 <= xp < self.img_w and 0 <= yp < self.img_h:
                points.append((int(xp), int(yp)))
        return points

    def pt_distance(self, p1, p2):
        return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

    def save_csv(self, img, datas, image_id, start_time,img_ori = None,img_depth = None):
        save_data = {}
        #识别开始时间
        #imageId = 1
        #识别结束时间
        end_time = time.time()
        #识别使用时间
        useT_time = end_time - start_time
        #文件名
        save_time = time.strftime('_%Y_%m_%d_%H_%M_%S')
        datas["useT_time"] = useT_time
        datas["save_time"] = save_time
        datas["image_id"] = image_id
        # 将'task'中的键提到上一级
        if 'task' in datas:
            for key, value in datas['task'].items():
                datas[key] = value  # 将'task'中的键值对添加到data中
            del datas['task']
        #dates.update(dict2)
        save_name = str(datas["dev"]) + "-" + str(datas["type"]) + save_time + "-" + str(image_id) 
        try:
            # 设置目录路径
            directory = "./data"
            # 获取当前的年月日
            today = datetime.datetime.now().strftime("%Y%m%d")
            # 构建完整的文件夹路径
            folderPath = os.path.join(directory, today)
            # 检查文件夹是否存在
            if not os.path.exists(folderPath):
                # 如果文件夹不存在，则创建它
                os.makedirs(folderPath)
            save_path = os.path.join(folderPath, save_name + ".jpg")
            print("save_path: ", save_path)
            cv2.imwrite(save_path, img)
            if img_ori is not None:
                save_path = os.path.join(folderPath, save_name + "_ori" + ".jpg")
                cv2.imwrite(save_path, img_ori)
            if img_depth is not None:
                save_path = os.path.join(folderPath, save_name + "_depth" + ".png")
                cv2.imwrite(save_path, img_depth)
            WriteDataFile = os.path.join(folderPath, 'Data.csv')
            with open(WriteDataFile, "a+", newline='') as f:
                # with open(birth_weight_file, "w") as f:
                writer = csv.writer(f)
                writer.writerow(datas.values())
                f.close()
        except Exception as e:
            print("save_csv error: {}".format(e))

def run_camera(sn,id):
    camera = CameraHandler(sn,id)
    camera.start_stream()
    try:
        while True:
            image_list = camera.get_images()
            if image_list:
                camera.process_images(image_list)
    finally:
        camera.stop_stream()
        camera.close_camera()
