<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>3406db83-c057-46bb-9c9e-5c23c151dfd4</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>robot.py</StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>PalletUnpackerVision</Name>
    <RootNamespace>PalletUnpackerVision</RootNamespace>
    <InterpreterId>CondaEnv|CondaEnv|PUVision</InterpreterId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="config.py" />
    <Compile Include="frame_get.py" />
    <Compile Include="frame_get_d435.py" />
    <Compile Include="Management.py" />
    <Compile Include="PalletUnpackerVision.py" />
    <Compile Include="pcammls.py" />
    <Compile Include="robot.py" />
    <Compile Include="YoloOnnx.py" />
  </ItemGroup>
  <ItemGroup>
    <InterpreterReference Include="CondaEnv|CondaEnv|PUVision" />
    <InterpreterReference Include="Global|PythonCore|3.9" />
    <InterpreterReference Include="Global|VisualStudio|env" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="params.yaml" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>