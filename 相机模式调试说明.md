# 相机模式调试信息说明

## 概述
本文档说明了相机模式下排序和直径显示功能的调试信息，帮助确定功能是否正常工作。

## 调试信息标签

### 1. [相机模式调试]
- **位置**: `frame_get.py` 中的相机模式相关函数
- **作用**: 跟踪相机模式下的数据处理流程
- **关键信息**:
  - 图像处理状态
  - result_list 数据
  - grasping_order_map 生成和传递
  - 任务执行结果

### 2. [YOLO调试]
- **位置**: `YoloOnnx.py` 中的 `draw_and_visualize` 函数
- **作用**: 跟踪YOLO模型推理和可视化过程
- **关键信息**:
  - 模型推理结果
  - grasping_order_map 接收状态
  - 抓取顺序编号显示
  - 直径计算过程

### 3. [ROBOT调试]
- **位置**: `robot.py` 中的主程序流程
- **作用**: 跟踪程序启动、模式选择和任务执行
- **关键信息**:
  - 程序启动和模式选择
  - 相机初始化和连接
  - 任务发送和执行状态
  - 上位机连接状态

## 调试信息流程

### 1. 程序启动流程
```
[ROBOT调试] ====== 本地图片模式启动 ======
[ROBOT调试] 启动本地图片模式处理: DC2
[ROBOT调试] 连接到上位机: xxx.xxx.xxx.xxx:xxxx
[ROBOT调试] 上位机连接成功，开始主循环
[ROBOT调试] 发送任务: box 到设备 DC2
[ROBOT调试] 相机 DC2 空闲，开始执行任务
[ROBOT调试] 任务执行结果: True
```

### 2. 相机模式处理流程
```
[相机模式调试] ====== 相机模式处理流程开始 ======
[相机模式调试] 接收到的image_list长度: X
[相机模式调试] 处理frame 0: streamID=XXX
[相机模式调试] 找到深度图像
[相机模式调试] 找到彩色图像
```

### 2. 图像处理流程
```
[相机模式调试] 调用get_img_result处理图像
[相机模式调试] get_img_result返回: ret=True, result=...
[相机模式调试] 将结果添加到result_list，当前长度: X
```

### 3. 排序算法流程
```
[相机模式调试] ====== 开始处理box任务 ======
[相机模式调试] 当前result_list长度: X
[相机模式调试] 尝试从result_list生成grasping_order_map
[相机模式调试] result_list内容: [...]
[相机模式调试] 排序后的箱子: [...]
[相机模式调试] 抓取信息: [...]
[相机模式调试] 生成的grasping_order_map: {...}
```

### 4. YOLO推理流程
```
[YOLO调试] ====== draw_and_visualize开始 ======
[YOLO调试] 接收到的grasping_order_map: {...}
[YOLO调试] 检测到X个目标
[YOLO调试] 模型推理完成，检测到X个目标
[YOLO调试] 开始调用draw_and_visualize，传递grasping_order_map: {...}
```

### 5. 抓取顺序显示流程
```
[YOLO调试] 处理箱子0，grasping_order_map: {...}
[YOLO调试] 箱子0显示抓取顺序编号: #1
[YOLO调试] 处理箱子1，grasping_order_map: {...}
[YOLO调试] 箱子1没有抓取顺序编号 (grasping_order_map={...}, idx=1)
```

## 功能验证要点

### 1. 程序启动验证
- ✅ **检查点**: `[ROBOT调试] ====== 本地图片模式启动 ======` 或 `[ROBOT调试] ====== 相机模式启动 ======`
- ✅ **期望**: 应该看到程序启动和模式选择信息
- ✅ **检查点**: `[ROBOT调试] 发送任务: box 到设备 DC2`
- ✅ **期望**: 应该看到任务发送信息

### 2. 排序功能验证
- ✅ **检查点**: `[相机模式调试] 排序后的箱子: [...]`
- ✅ **期望**: 应该看到按优先级排序的箱子列表
- ✅ **检查点**: `[相机模式调试] 生成的grasping_order_map: {...}`
- ✅ **期望**: 应该看到箱子索引到抓取顺序的映射

### 2. 直径计算验证
- ✅ **检查点**: `[调试] 像素差法直径: XXX mm`
- ✅ **期望**: 应该看到每个箱子的直径计算结果
- ✅ **检查点**: `[调试] 平均直径: XXX`
- ✅ **期望**: 应该看到最终的平均直径值

### 3. 可视化显示验证
- ✅ **检查点**: `[YOLO调试] 箱子X显示抓取顺序编号: #Y`
- ✅ **期望**: 应该看到抓取顺序编号的显示信息
- ✅ **检查点**: `[YOLO调试] draw_and_visualize完成，返回X个结果`
- ✅ **期望**: 应该看到最终的处理结果

## 常见问题排查

### 1. 没有抓取顺序编号显示
**可能原因**:
- grasping_order_map 为空或None
- 箱子索引不匹配
- 排序算法未正确执行

**排查步骤**:
1. 检查 `[相机模式调试] 生成的grasping_order_map: {...}`
2. 检查 `[YOLO调试] 接收到的grasping_order_map: {...}`
3. 检查 `[YOLO调试] 箱子X没有抓取顺序编号`

### 2. 直径计算异常
**可能原因**:
- 深度图像数据无效
- 相机标定参数错误
- 箱子检测不准确

**排查步骤**:
1. 检查 `[调试] 中心点深度值: XXX`
2. 检查 `[调试] 像素差法直径: XXX mm`
3. 检查 `[调试] 平均直径: XXX`

### 3. 排序算法异常
**可能原因**:
- result_list 数据格式错误
- 排序算法参数不当
- 箱子位置信息不准确

**排查步骤**:
1. 检查 `[相机模式调试] result_list内容: [...]`
2. 检查 `[相机模式调试] 排序后的箱子: [...]`
3. 检查 `[抓取顺序调试] 最高层Z=XXX，箱子数量=X`

## 运行测试

直接运行 `robot.py` 即可看到调试信息：

```bash
python robot.py
```

根据配置的 `LOCAL_IMAGE_MODE` 参数，程序会：
1. **本地图片模式** (`LOCAL_IMAGE_MODE = True`): 直接处理本地图片
2. **相机模式** (`LOCAL_IMAGE_MODE = False`): 连接真实相机进行处理

调试信息会在控制台实时显示，包括：
- 程序启动和模式选择
- 任务发送和执行
- 图像处理和排序算法
- YOLO推理和可视化

## 调试信息开关

如果需要关闭调试信息，可以：
1. 注释掉相关的 `print` 语句
2. 或者添加调试开关变量来控制输出

```python
DEBUG_MODE = False  # 设置为False关闭调试信息

if DEBUG_MODE:
    print(f"[相机模式调试] 调试信息")
``` 