#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版图片复制脚本
功能：复制文件夹中的PNG图片和对应的ORI.JPG图片到另一个文件夹
"""

import os
import shutil
from pathlib import Path
import glob


def find_image_pairs(source_dir):
    """
    在源目录中查找PNG图片和对应的ORI.JPG图片对
    
    Args:
        source_dir (str): 源目录路径
        
    Returns:
        list: 包含(源PNG路径, 源ORI.JPG路径)的元组列表
    """
    image_pairs = []
    
    # 递归查找所有PNG文件
    png_pattern = os.path.join(source_dir, "**", "*.png")
    png_files = glob.glob(png_pattern, recursive=True)
    
    print(f"找到 {len(png_files)} 个PNG文件")
    
    for png_file in png_files:
        # 获取PNG文件的基础名称（不包含扩展名）
        png_path = Path(png_file)
        base_name = png_path.stem
        
        # 查找对应的ORI.JPG文件
        # 尝试多种可能的命名模式
        possible_ori_names = [
            f"{base_name}_ori.jpg",
            f"{base_name}_ori.JPG", 
            f"{base_name}.ori.jpg",
            f"{base_name}.ori.JPG",
            f"{base_name}_original.jpg",
            f"{base_name}_original.JPG"
        ]
        
        ori_file = None
        for ori_name in possible_ori_names:
            ori_path = png_path.parent / ori_name
            if ori_path.exists():
                ori_file = str(ori_path)
                break
        
        if ori_file:
            image_pairs.append((png_file, ori_file))
            print(f"✓ 找到图片对: {os.path.basename(png_file)} <-> {os.path.basename(ori_file)}")
        else:
            print(f"⚠ 警告: 未找到 {os.path.basename(png_file)} 对应的ORI文件")
    
    return image_pairs


def copy_image_pairs(image_pairs, target_dir):
    """
    复制图片对到目标目录
    
    Args:
        image_pairs (list): 图片对列表
        target_dir (str): 目标目录路径
    """
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)
        print(f"创建目标目录: {target_dir}")
    
    copied_count = 0
    failed_count = 0
    
    for png_file, ori_file in image_pairs:
        try:
            png_path = Path(png_file)
            ori_path = Path(ori_file)
            
            # 复制到目标目录（保持文件名）
            target_png = Path(target_dir) / png_path.name
            target_ori = Path(target_dir) / ori_path.name
            
            # 复制文件
            shutil.copy2(png_file, target_png)
            shutil.copy2(ori_file, target_ori)
            
            copied_count += 1
            print(f"✓ 已复制: {png_path.name} 和 {ori_path.name}")
            
        except Exception as e:
            failed_count += 1
            print(f"✗ 复制失败: {os.path.basename(png_file)} -> {e}")
    
    print(f"\n复制完成!")
    print(f"✓ 成功复制: {copied_count} 对图片")
    if failed_count > 0:
        print(f"✗ 失败: {failed_count} 对图片")


def main():
    """主函数"""
    print("=" * 50)
    print("PNG和ORI.JPG图片复制工具")
    print("=" * 50)
    
    # 获取用户输入
    source_dir = input("请输入源文件夹路径: ").strip()
    target_dir = input("请输入目标文件夹路径: ").strip()
    
    # 检查源目录是否存在
    if not os.path.exists(source_dir):
        print(f"错误: 源目录不存在: {source_dir}")
        return
    
    print(f"\n源目录: {source_dir}")
    print(f"目标目录: {target_dir}")
    print("-" * 50)
    
    # 查找图片对
    image_pairs = find_image_pairs(source_dir)
    
    if not image_pairs:
        print("未找到任何PNG图片对")
        return
    
    print(f"\n找到 {len(image_pairs)} 对图片")
    print("-" * 50)
    
    # 确认操作
    confirm = input(f"确认复制 {len(image_pairs)} 对图片到 '{target_dir}'? (y/n): ").strip().lower()
    
    if confirm in ['y', 'yes', '是']:
        copy_image_pairs(image_pairs, target_dir)
    else:
        print("操作已取消")


if __name__ == "__main__":
    main() 