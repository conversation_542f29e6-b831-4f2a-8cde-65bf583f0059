import cv2
import numpy
import sys
import os
import datetime
import time
import csv
import threading
from threading import Thread ,Timer
import logging
import config
#import pupil_apriltags as apriltag
import random
import math
import numpy as np
import queue
import YoloOnnx
import copy
import open3d as o3d
import pyrealsense2 as rs


QRCODE = "CV2"

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

'''
托盘尺寸： 外 200*140 内 192*136
'''
    
class D435CameraHandler:
    def __init__(self, sn, id, result_queue):
        self.sn = sn
        self.id = id
        self.getImg = False
        self.offline = False
        self.result_queue = result_queue
        self.isbusy = False
        self.task = None
        self.start = False
        self.msg = None
        self.depth_calib_intr = [0,0,0,0,0,0]
        self.img_w = 1280
        self.img_h = 960
        self.start_time = time.time()
        self.result_list = []
        self.frame_count = 0
        self.MAX_FRAME_COUNT = 10
        self.image_id = 0
        self.pallet_center = {"x":0, "y":0, "z":0, "angle":0, "l":500, "w":1000, "h":2300}

        if self.id =="DC1":
            self.pallet_center = {"x":50, "y":-100, "z":2230, "angle":0, "l":1800, "w":1180, "h":3990}#{"x":0, "y":0, "z":2250, "angle":0, "l":1820, "w":1160, "h":3990}
        elif self.id =="DC2":
            self.pallet_center = {"x":80, "y":0, "z":2230, "angle":0, "l":1800, "w":1180, "h":3990}
        elif self.id =="DC3":
            self.pallet_center = {"x":-10, "y":-80, "z":2230, "angle":0, "l":1800, "w":1180, "h":3990}
        self.xyz_range = (self.pallet_center['x'] - self.pallet_center['l'] / 2 - 300, self.pallet_center['x'] + self.pallet_center['l'] / 2 + 300,
                          self.pallet_center['y'] - self.pallet_center['w'] / 2 - 150, self.pallet_center['y'] + self.pallet_center['w'] / 2 + 150,
                          self.pallet_center['z'] - 150, self.pallet_center['h'] + 150)
        self.context = rs.context()

        # 获取设备列表
        self.devices = self.context.query_devices()

        connectFlag=False
        # 遍历设备列表并打印设备信息
        for i, device in enumerate(self.devices):
            connectFlag=True
            print(f"Device {i}:")
            print(f"Name: {device.get_info(rs.camera_info.name)}")
            print(f"Serial Number: {device.get_info(rs.camera_info.serial_number)}")
            print(f"Firmware Version: {device.get_info(rs.camera_info.firmware_version)}")
            
        if connectFlag == True:
            # 创建一个管道
            self.pipeline = rs.pipeline()# 用于获取流数据
 
            # 创建设备配置对象
            self.config = rs.config()
            self.config.enable_stream(rs.stream.color, 1280, 720, rs.format.bgr8, 6)  # RGB流，640x480分辨率（图像中包含的像素数量），BGR8格式，6fps（D435相机的最低帧率）
            self.config.enable_stream(rs.stream.depth, 1280, 720, rs.format.z16, 6)   # 深度流，640x480分辨率（图像中包含的像素数量），Z16格式，6fps（D435相机的最低帧率）
 
            # 启动管道
            self.profile = self.pipeline.start(self.config)
            self.align_to = rs.stream.color
            self.align = rs.align(self.align_to)
        else:
            print("连接相机失败")
            self.offline = True

        if QRCODE == "CV2":
            self.detector = cv2.aruco.getPredefinedDictionary(cv2.aruco.DICT_APRILTAG_36H11)
        else:
            self.detector = apriltag.Detector(families='tag36h11')

    def depth2xyz(self, px, py, depth):
        cx = self.depth_calib_intr[2]#663.191772
        cy = self.depth_calib_intr[5]#494.933533
        fx = self.depth_calib_intr[0]#1044.018799
        fy = self.depth_calib_intr[4]#1044.018799
        z = int(depth)
        x = int((px - cx) * z / fx)
        y = int((py - cy) * z / fy)
        result = [x, y, z]
        return result

    def xyz2depth(self, x, y, z):
        cx = self.depth_calib_intr[2]
        cy = self.depth_calib_intr[5]
        fx = self.depth_calib_intr[0]
        fy = self.depth_calib_intr[4]
        px = int(x * fx / z + cx)
        py = int(y * fy / z + cy)
        result = [px, py]
        return result
    
    def process_images(self, depth_image,color_image):
        try:
            #mat_undistortion_color  np_img_registration_depth
            draw_img = copy.deepcopy(color_image)
            #tl_pt, br_pt = self.get_box_draw_rect(self.pallet_center["x"], self.pallet_center["y"], self.pallet_center["z"], self.pallet_center["l"], self.pallet_center["w"])
            #cv2.rectangle(draw_img,(tl_pt[0], tl_pt[1]),(br_pt[0], br_pt[1]), (0, 0, 125), 2)
            scale_factor = 0.5

            # 计算新的尺寸
            new_width = int(draw_img.shape[1] * scale_factor)
            new_height = int(draw_img.shape[0] * scale_factor)
            # 缩放图片
            resized_img = cv2.resize(draw_img, (new_width, new_height), interpolation=cv2.INTER_AREA)
            # 显示图片
            #cv2.imshow("Resized Image", resized_img)
            cv2.imshow('color_image-' + self.id, resized_img)
            cv2.waitKey(1)

            if self.start:
                now = time.time()
                if abs(now - self.start_time) > 10:
                    self.msg["data"]["result"] = "fail"
                    self.msg["data"]["codelist"] = [1]
                    self.msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                    self.result_queue.put(self.msg)
                    self.start = False
                    self.isbusy = False
                elif depth_image is not None and color_image is not None:
                    self.frame_count += 1
                    if self.frame_count >= self.MAX_FRAME_COUNT:
                        self.msg["data"]["result"] = "fail"
                        self.msg["data"]["codelist"] = [1]
                        self.msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                        self.result_queue.put(self.msg)
                        self.start = False
                        self.isbusy = False
                    else:
                        save_img = copy.deepcopy(color_image)
                        
                        ret,result = self.get_img_result(depth_image,color_image)
                        if ret:
                            self.result_list.append(result) 
                            if len(self.result_list) >= 2:
                                ret, result_msg = self.result_processing(self.result_list,color_image)
                                if ret:
                                    self.image_id += 1
                                    self.save_csv(color_image, result_msg, self.image_id, self.start_time,img_ori = save_img,img_depth = depth_image)
                                    result_msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                                    self.result_queue.put(result_msg)
                                    self.start = False
                                    self.isbusy = False
                                    print(f"----------------------")
                                    print(f"----------------------")
                                    print(f"第{self.image_id}次测试")
                                    print(f"----------------------")
                                    print(f"----------------------")
                                    if self.image_id >= 10:
                                        self.image_id = 0
                                else:
                                    self.msg["data"]["result"] = "fail"
                                    self.msg["data"]["codelist"] = [1]
                                    self.msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                                    self.result_queue.put(self.msg)
                                    self.start = False
                                    self.isbusy = False
        except Exception as e:
            logging.error("Error processing images: %s", e)
            return None, None, None


    def run_task(self, msg):
        if self.isbusy:
            config.log.logger.warning("Camera is busy, cannot run task")
            return False
        else:
            ret, self.msg = self.get_msg(msg)
            if ret:
                self.start_time = time.time()
                self.result_list = []
                self.frame_count = 0
                self.isbusy = True
                self.start = True
                self.task = msg
                return ret
            else:
                return False

    def get_msg(self, msg):
        try:
            result_msg = {} 
            result_msg["eid"] = msg["eid"]
            result_msg["dev"] = msg["dev"]
            result_msg["type"] = msg["type"] + "Feedback"
            result_msg["taskNum"] = msg["taskNum"]
            result_msg["sendTime"] = msg["sendTime"]
            result_msg["data"] = {}
            if msg["type"] == "selfCheck":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["codelist"] = [0]
            elif msg["type"] == "box":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["points"] = []
            elif msg["type"] == "palletCenter":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["center"] = [0,0,0]
                result_msg["data"]["angle"] = 0
            elif msg["type"] == "placementLocation":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["points"] = [] #xyzw
                
            elif msg["type"] == "beltBox":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["box"] = "existence"
                result_msg["data"]["point"] = [] #xyzw
            elif msg["type"] == "QRCode":
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["coordinates"] = [] #id x y z px py

            else:
                return False,None
            return True,result_msg
        except Exception as e:
            config.log.logger.error("Error getting message: %s", e)
            return False,None

    def result_processing(self, result_list, img):
        try:
            if self.task["type"] == "selfCheck":
                self.msg["data"]["result"] = "pass"
                return True, self.msg
            if self.task["type"] == "box":
                max_result = max(result_list, key=lambda x: x[4] * x[5])
                #max_result = max(result_list, key=lambda x: x[0])
                self.msg["data"]["points"] = [max_result]
                print("box",self.msg["data"]["points"])
                self.msg["data"]["result"] = "pass"
                self.msg["data"]["code"] = [0]
                
            elif self.task["type"] == "palletCenter":
                self.msg["data"]["result"] = "pass"
                self.msg["data"]["code"] = [0]
                pallet_l = 2000
                pallet_w = 1400
                lt = [self.pallet_center["x"] - pallet_l/2, self.pallet_center["y"] - pallet_w / 2, self.pallet_center["z"]]
                rt = [self.pallet_center["x"] + pallet_l/2, self.pallet_center["y"] - pallet_w / 2, self.pallet_center["z"]]
                lb = [self.pallet_center["x"] - pallet_l/2, self.pallet_center["y"] + pallet_w / 2, self.pallet_center["z"]]
                rb = [self.pallet_center["x"] + pallet_l/2, self.pallet_center["y"] + pallet_w / 2, self.pallet_center["z"]]
                center = [self.pallet_center["x"], self.pallet_center["y"], self.pallet_center["z"]]
                self.msg["data"]["center"] = [lt,rt,lb,rb,center]
                self.msg["data"]["angle"] = 0
                
            elif self.task["type"] == "placementLocation":
                self.msg["data"]["result"] = "pass"
                self.msg["data"]["code"] = [0]
                self.msg["data"]["points"] = [] #xyzw
                #self.msg["data"]["minH"] = self.pallet_center['h'] - self.task["task"]["height"]

                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 0.5
                color = (125, 125, 0)
                thickness = 2
                # 定义文本行距
                line_spacing = 20

                first_grid = result_list[0]
                for grid in range(1, len(result_list)):
                    for i, element in enumerate(result_list[grid]):
                        first_grid[i]["ptcnt"] += element["ptcnt"]
                pts = []
                for grid in first_grid:
                    grid["ptcnt"] /= len(result_list)
                    if grid['ptcnt'] <= (150 * len(result_list)):
                        cv2.rectangle(img,(grid['tl_pt'][0], grid['tl_pt'][1]),(grid['br_pt'][0], grid['br_pt'][1]), (0, 0, 255), 2)
                    else:
                        cv2.rectangle(img,(grid['tl_pt'][0], grid['tl_pt'][1]),
                            (grid['br_pt'][0], grid['br_pt'][1]), (125, 125, 0), 2)
                        cv2.putText(img, f"x:{grid['x']}", (int(grid['tl_pt'][0])+20, int(grid['tl_pt'][1])+20), font, font_scale, color, thickness)
                        cv2.putText(img, f"y:{grid['y']}", (int(grid['tl_pt'][0])+20, int(grid['tl_pt'][1])+40), font, font_scale, color, thickness)
                        cv2.putText(img, f"z:{grid['z']}", (int(grid['tl_pt'][0])+20, int(grid['tl_pt'][1])+60), font, font_scale, color, thickness)
                        pt = [grid["x"], grid["y"], grid["z"], 0]
                        pts.append(pt)
                z_values = [pt[2] for pt in pts] 
                z_max = max(z_values)
                z_min = min(z_values)
                if abs(z_min - z_max) >=  self.task["task"]["height"] * 2:
                    self.msg["data"]["result"] = "fail"
                    self.msg["data"]["code"] = [1]#高度异常

                z_max = int(z_max - self.task["task"]["height"] * 0.9)
                points = []
                for pt in pts:
                    if pt[2] >= z_max:
                        points.append(pt)
                for grid in first_grid:
                    if grid['z'] >= z_max:
                        cv2.rectangle(img,(grid['tl_pt'][0], grid['tl_pt'][1]),
                                (grid['br_pt'][0], grid['br_pt'][1]), (255, 255, 0), 2)
                        cv2.putText(img, f"x:{grid['x']}", (int(grid['tl_pt'][0])+20, int(grid['tl_pt'][1])+20), font, font_scale, color, thickness)
                        cv2.putText(img, f"y:{grid['y']}", (int(grid['tl_pt'][0])+20, int(grid['tl_pt'][1])+40), font, font_scale, color, thickness)
                        cv2.putText(img, f"z:{grid['z']}", (int(grid['tl_pt'][0])+20, int(grid['tl_pt'][1])+60), font, font_scale, color, thickness)

                self.msg["data"]["points"] = points
                self.msg["data"]["minH"] = z_min
                cv2.imshow('placementLocation-' + self.id, img)
                cv2.waitKey(1)
                cv2.waitKey(1)
            elif self.task["type"] == "beltBox":
                self.msg["data"]["result"] = "pass"
                self.msg["data"]["code"] = [0]
                self.msg["data"]["box"] = "existence"
                self.msg["data"]["point"] = [] #xyzw
                
            elif self.task["type"] == "QRCode":
                self.msg["data"]["result"] = "pass"
                self.msg["data"]["code"] = [0]
                ids = [result[0] for result in result_list]
                if len(set(ids)) != 1:
                    return False,None
                get_id = ids[0]
                # 使用 zip 和列表推导式求和
                sum_values = [sum(values) for values in zip(*result_list)][1:]  # 跳过第一个元素，因为它是 id
                # 计算平均值
                average_values = [int(val / len(result_list)) for val in sum_values]
                #sum_values = sum(result[1:] for result in result_list)  # 求和
                #average_values = [int(val / len(result_list)) for val in sum_values]
                self.msg["data"]["coordinates"] = [get_id] + average_values
                px = self.msg["data"]["coordinates"][4]
                py = self.msg["data"]["coordinates"][5]
                cv2.circle(img, (px, py), 3, (0, 255, 0), -1)
                cv2.putText(img, f"id:{get_id} x:{average_values[0]} y:{average_values[1]} z:{average_values[2]}", (px - 90, py + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
                cv2.imshow('QRCode-' + self.id, img)
                cv2.waitKey(1)
                
            else:
                return False,None
            return True,self.msg
        except Exception as e:
            config.log.logger.error("Error processing results: %s", e)
            return False, None

    def get_img_result(self, np_depth, mat_color):
        ret = False
        result = None
        #mat_color = cv2.imread('4_color.png')
        #np_depth = cv2.imread('4_depth.png', cv2.IMREAD_UNCHANGED)
        try:
            if self.task["type"] == "QRCode":
                ret, result = self.task_qrcode(np_depth,mat_color)
            elif self.task["type"] == "box":
                ret, result, mat_color = self.task_box(np_depth,mat_color)
                if ret and mat_color is not None:
                    cv2.imshow('box-' + self.id, mat_color)
                    cv2.waitKey(1)
            elif self.task["type"] == "palletCenter":
                ret = True                
                result = self.pallet_center
            elif self.task["type"] == "placementLocation":
                ret, result = self.task_placementLocation(np_depth,mat_color)
            return ret,result
        except Exception as e:
            config.log.logger.error("Error processing images: %s", e)
            return False, None

    def task_qrcode(self, np_depth, mat_color): #二维码检测
        try:
            for i in range(4):
                alp = 0.5 + 0.3 * i
                imgA = cv2.convertScaleAbs(mat_color, alpha=alp)
                imgA = cv2.GaussianBlur(imgA, (3, 3), 0)
                grayA = cv2.cvtColor(imgA, cv2.COLOR_BGR2GRAY)
                if QRCODE == "CV2":
                    corners, ids, rejectedImgPoints = cv2.aruco.detectMarkers(grayA, self.detector)
                    if ids is not None:
                        for i, (corner, id) in enumerate(zip(corners, ids)):
                            # 计算中心坐标
                            center_x = int(sum(corner[0][:, 0]) / len(corner[0]))
                            center_y = int(sum(corner[0][:, 1]) / len(corner[0]))
                            (px, py) = (int(center_x), int(center_y))
                            # 输出ID和中心坐标
                            print(f"Tag {id[0]} center at: ({center_x}, {center_y})")
                            tag_id = id[0]
                            ret, result = self.get_code_depth(np_depth, px, py, 25, tag_id)
                            print("result",result)
                            if ret:
                                return True,result
                else:
                    results = self.detector.detect(grayA)
                    if results is not None:
                        for r in results:
                            pt = self.get_code_points(r)
                            (px, py) = (int(r.center[0]), int(r.center[1]))
                            tag_id = r.tag_id
                            ret, result = self.get_code_depth(np_depth, px, py, pt[2], tag_id)
                            print("result",result)
                            if ret:
                                return True,result
            return False, None
        except Exception as e:
            config.log.logger.error("Error task_qrcode: %s", e)
            return False, None

    def task_placementLocation(self, np_depth, mat_color): #放置位置检测
        try:
            
            
            np_depth = np.asanyarray(np_depth)
            
            self.pinhole_camera_intrinsic = o3d.camera.PinholeCameraIntrinsic(
                        self.img_w, self.img_h, self.depth_calib_intr[0], self.depth_calib_intr[4], self.depth_calib_intr[2], self.depth_calib_intr[5])
            
            pallet_l = self.pallet_center["l"]  #长
            pallet_w = self.pallet_center["w"]  #宽
            pallet_h = self.pallet_center["h"]  #高
            pallet_center_x = self.pallet_center["x"]  #x
            pallet_center_y = self.pallet_center["y"]  #y
            box_l = self.task["task"]["length"]
            box_w = self.task["task"]["width"]
            box_h = self.task["task"]["height"]
            num_boxes_along_length = pallet_l // box_l
            num_boxes_along_width = pallet_w // box_w
            box_l_gap = int(pallet_l / num_boxes_along_length)
            box_w_gap = int(pallet_w / num_boxes_along_width)
            box_coordinates = []
            box_add = 10
            for i in range(num_boxes_along_length):
                for j in range(num_boxes_along_width):
                    # 计算纸箱中心的x坐标
                    box_x = pallet_center_x + (i - (num_boxes_along_length - 1) / 2) * (box_l_gap + box_add)
                    # 计算纸箱中心的y坐标
                    box_y = pallet_center_y + (j - (num_boxes_along_width - 1) / 2) * (box_w_gap + box_add)
                    draw_h = pallet_h - box_h
                    tl_pt, br_pt = self.get_box_draw_rect(box_x, box_y, draw_h, box_l, box_w)
                    element = {'x':box_x, 'y':box_y, 'z':pallet_h, 'w':box_w, 'l':box_l, 'tl_pt':tl_pt, 'br_pt':br_pt, 'ptcnt':0}
                    box_coordinates.append(element)
                    
            if len(box_coordinates) > 0:
                
                img_depth = o3d.geometry.Image(np_depth)
                img_color = o3d.geometry.Image(mat_color)
                # #从彩色图、深度图创建RGBD
                # rgbd_image = o3d.geometry.RGBDImage.create_from_color_and_depth(
                #     img_color, img_depth, convert_rgb_to_intensity=False
                # )

                # # 相机外参，表示相机的位置和方向，通常是一个单位矩阵如果相机是静止不动的
                # extrinsic = np.eye(4)

                # # 创建点云
                # pcd = o3d.geometry.PointCloud.create_from_rgbd_image(
                #     rgbd_image,
                #     self.pinhole_camera_intrinsic,  # 使用内参矩阵
                #     extrinsic,  # 外参矩阵
                #     depth_trunc=5.0,  # 指定深度截断值，超过这个距离的点将不会被包含在点云中
                #     project_valid_depth_only=True  # 只投影有效的深度值
                # )
                rgbd = o3d.geometry.RGBDImage.create_from_color_and_depth(img_color, img_depth,depth_trunc=4.1)
                # 创建pcd
                pcd = o3d.geometry.PointCloud.create_from_rgbd_image(rgbd,self.pinhole_camera_intrinsic)#,depth_trunc=5.0
                #voxel_down_pcd = pcd.voxel_down_sample(voxel_size=0.01)
                #cl, ind = voxel_down_pcd.remove_radius_outlier(nb_points=16, radius=0.05)
                #pcd.points = cl.points
                points = np.asarray(pcd.points)
                maxpt = 0
                for pt in points:
                    if maxpt < pt[2]:
                        maxpt = pt[2]
                #o3d.visualization.draw_geometries([pcd])
                location_grid = []
                min_bound = [(pallet_center_x - pallet_l / 2) / 1000, (pallet_center_y - pallet_w / 2)/1000, pallet_h/1000 - 2.0]
                max_bound = [(pallet_center_x + pallet_l / 2) / 1000, (pallet_center_y + pallet_w / 2)/1000, pallet_h/1000 + 0.15]
                cropped_indice = np.where(((points >= min_bound) & (points <= max_bound)).all(axis=1))[0]
                pcd = pcd.select_by_index(cropped_indice)
                points = np.asarray(pcd.points)
                #显示
                #o3d.visualization.draw_geometries([cropped_point])

                #min_bounds = np.array([[element['x']/1000 - box_l/2/1000, element['y']/1000 - box_w/2/1000, element['z']/1000 - 2.5] for element in box_coordinates])
                #max_bounds = np.array([[element['x']/1000 + box_l/2/1000, element['y']/1000 + box_w/2/1000, element['z']/1000 - 0.15] for element in box_coordinates])

                #bounds = np.array([[element['x']/1000 - box_l/2/1000, element['x']/1000 + box_l/2/1000, 
                #                    element['y']/1000 - box_w/2/1000, element['y']/1000 + box_w/2/1000,
                #                    element['z']/1000 - 2.5], element['z']/1000 + 0.1] for element in box_coordinates])

                #for i, element in enumerate(box_coordinates):
                #    min_bound = min_bounds[i]
                #    max_bound = max_bounds[i]
                #    cropped_indices = np.where(((points >= min_bound) & (points <= max_bound)).all(axis=1))[0]
                #    cropped_point_cloud = pcd.select_by_index(cropped_indices)
                #    num_points_in_cropped_region = len(cropped_point_cloud.points)
                #    element['ptcnt'] += num_points_in_cropped_region
                #    location_grid.append(element)
                #    cropped_point_cloud = point_cloud.crop(bounds)

                min_bounds = np.array([[element['x']/1000 - box_l/2/1000, element['y']/1000 - box_w/2/1000, element['z']/1000 - 1.8] for element in box_coordinates])
                max_bounds = np.array([[element['x']/1000 + box_l/2/1000, element['y']/1000 + box_w/2/1000, element['z']/1000 + 0.15] for element in box_coordinates])

                #obb = sub_cloud.get_axis_aligned_bounding_box()
                #obb = sub_cloud.get_oriented_bounding_box()
                #obb.color = (1, 0, 0)
                obbs = []
                for i, element in enumerate(box_coordinates):
                    min_bound = min_bounds[i]
                    max_bound = max_bounds[i]
                    cropped_indices = np.where(((points >= min_bound) & (points <= max_bound)).all(axis=1))[0]
                    cropped_point_cloud = pcd.select_by_index(cropped_indices)
                    nb_neighbors = 15  # 指定邻居点的数量
                    std_ratio = 2.0  # 标准偏差比率
                    voxel_size = 0.03  # 体素大小，单位与点云数据单位一致
                    cleaned_pcd = cropped_point_cloud.voxel_down_sample(voxel_size)
                    cleaned_pcd, ind = cleaned_pcd.remove_statistical_outlier(nb_neighbors, std_ratio)
                    #cleaned_pcd, ind = cleaned_pcd.remove_radius_outlier(nb_points=30, radius=0.05)
                    if len(cleaned_pcd.points)<= 10:
                        continue
                    obb = cleaned_pcd.get_axis_aligned_bounding_box()
                    #o3d.visualization.draw_geometries([cleaned_pcd])
                    if obb is not None:
                        obb.color = (1, 0, 0)
                        obbs.append(obb)

                        num_points_in_cropped_region = len(cropped_point_cloud.points)
                        element['ptcnt'] += num_points_in_cropped_region

                        element['z'] = int(obb.min_bound[2] * 1000)
                        tl_pt, br_pt = self.get_box_draw_rect(element['x'], element['y'], element['z'], self.task["task"]["length"], self.task["task"]["width"])
                        element['tl_pt'] = tl_pt
                        element['br_pt'] = br_pt
                        
                        location_grid.append(element)
                    

                #o3d.visualization.draw_geometries([pcd] + obbs)


                return True, location_grid
            else:
                return False, None
        except Exception as e:
            config.log.logger.error("Error task_placementLocation: %s", e)
            return False, None
    def get_box_draw_rect(self, x, y, z, l, w):
        tl_pt = self.xyz2depth(x - l / 2, y - w / 2, z)
        br_pt = self.xyz2depth(x + l / 2, y + w / 2, z)
        return tl_pt, br_pt


    def task_box(self, np_depth, mat_color): #检测
        try:
            box_results = []
            max_box = []
            #mat_color = cv2.imread("./test_1.png")
            output, results = self.task_box_process(mat_color, np_depth, config.box_model)
            if results is not None:                 
                #x,y,z,a,px,py,w,l
                minz = 4000
                for result in results:
                    x,y,z,a,px,py,w,l = result
                    if minz > z:
                        minz = z
                    w_mm, l_mm = self.get_box_wl(result)
                    box_results.append([x,y,z,a,l_mm,w_mm])
                if len(box_results) > 0:
                    max_box = max(box_results, key=lambda x: x[4] * x[5])
                    #max_boxs.append(max_box)
                return True,max_box,output
            else:
                return False, None, None
        except Exception as e:
            config.log.logger.error("Error task_box: %s", e)
            return False, None, None
    
    def get_box_wl(self, box):
        x,y,z,a,px,py,w,l = box
        pl = self.depth2xyz(-l/2, 0, z)
        pr = self.depth2xyz(l/2, 0, z)
        l_mm = int(self.pt_distance(pl, pr))
        w_mm = int(l_mm * w / l)
        return w_mm, l_mm
    
    def task_box_process(self, img, depthimg, model):
        try:
            starTime = time.time()
            # 推理
            boxes, segments, _ = model(img, conf_threshold=0.35, iou_threshold=0.45)
            # 画图
            
            if len(boxes) > 0:
                output_image, result = model.draw_and_visualize(img, boxes, segments, depthimg, self.depth_calib_intr, vis=False, save=True ,xyz_range = self.xyz_range)
            else:
                output_image = img
                result = None            
            print("图片完成检测")
            useTime = time.time() - starTime
            useTime = round(useTime, 2)
            textTime = f"useTime: {useTime} seconds - {self.image_id}"
            cv2.putText(output_image, textTime, (int(10), int(30)), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)
            # cv2.imshow("box"+self.id, output_image)
            # cv2.waitKey(1)
            return output_image, result

        except Exception as e:            
            config.log.logger.error("Error task_box_process: %s", e)
            return None, None

    def run_camera_in_main_thread(self):
        try:
            print('run_camera_in_main_thread:')
            while True:
                self.frames = self.pipeline.wait_for_frames()
                self.aligned_frames = self.align.process(self.frames)
                depth_frame = self.aligned_frames.get_depth_frame()
                color_frame = self.aligned_frames.get_color_frame()
                if self.depth_calib_intr[2] == 0:
                    intr = depth_frame.profile.as_video_stream_profile().intrinsics
                    self.img_w = intr.width
                    self.img_h = intr.height                    
                    self.depth_calib_intr = [intr.fx,0,intr.ppx,0,intr.fy,intr.ppy]
                time.sleep(0.01)
                if depth_frame is not None and color_frame is not None:
                    self.getImg = True
                    depth_image = np.asanyarray(depth_frame.get_data())
                    color_image = np.asanyarray(color_frame.get_data())
                    self.process_images(depth_image,color_image)
                    
        except Exception as e:
            print(f"self.pipeline.stop() {e}")
            self.pipeline.stop()
        finally:
            print(f"self.pipeline.stop()")

    def get_code_depth(self, depthImage,  cX, cY, minDistance, id):
        """
        根据深度图像和编码信息获取编码的深度信息。
        :param depthImage: 深度图像，用于获取编码区域的深度信息
        :param code: 编码信息，包含编码的位置和尺寸等信息
        :return: 返回一个元组，包含编码
        """
        try:
            distancesList = []
            # (cX, cY) = (int(code.center[0]), int(code.center[1]))
            # distances = [self.ptDistance((cX, cY), corner) for corner in code.corners]
            # minDistance = min(distances)
            randomPoints = self.generate_random_points(cX, cY, minDistance, 50)
            for (px,py) in randomPoints:
                dist = depthImage[py,px]
                if dist != 0 and dist < 4000 and dist > 350:
                    distancesList.append(dist)
            if len(distancesList) > 0:
                meanDistance = int(np.mean(distancesList))
                ptDepth = self.depth2xyz(cX, cY, meanDistance)
                return True, [id, int(ptDepth[0]), int(ptDepth[1]), int(ptDepth[2]), cX, cY]
        except:
            return False, None

    def get_code_points(self, code):
        """
        计算给定二维码的中心坐标和其到四个角的最短距离。
        :param code: 二维码对象，包含二维码的中心和四个角的信息。
        :return: 返回一个元组，包含二维码中心的X坐标、Y坐标以及中心到四个角的最短距离（单位为像素）。
        """
        (cX, cY) = (int(code.center[0]), int(code.center[1]))
        distances = [self.pt_distance((cX, cY), corner) for corner in code.corners]
        minDistance = min(distances)
        return cX, cY, int(minDistance)

    def generate_random_points(self, x, y, R, n):
        """
        生成在以(x, y)为圆心、半径为R的圆内的随机点。
        param:
        x, y: 圆心的坐标
        R: 圆的半径
        n: 需要生成的点的数量
        return:
        一个包含n个在圆内的随机点的列表，每个点都是一个(x, y)坐标元组。
        """
        points = []
        while len(points) < n:
            theta = random.uniform(0, 2 * np.pi)
            r = random.uniform(0, R)
            xp = x + r * np.cos(theta)
            yp = y + r * np.sin(theta)
            if 0 <= xp < self.img_w and 0 <= yp < self.img_h:
                points.append((int(xp), int(yp)))
        return points

    def pt_distance(self, p1, p2):
        return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

    def save_csv(self, img, datas, image_id, start_time,img_ori = None,img_depth = None):
        save_data = {}
        #识别开始时间
        #imageId = 1
        #识别结束时间
        end_time = time.time()
        #识别使用时间
        useT_time = end_time - start_time
        #文件名
        save_time = time.strftime('_%Y_%m_%d_%H_%M_%S')
        datas["useT_time"] = useT_time
        datas["save_time"] = save_time
        datas["image_id"] = image_id
        # 将'task'中的键提到上一级
        if 'task' in datas:
            for key, value in datas['task'].items():
                datas[key] = value  # 将'task'中的键值对添加到data中
            del datas['task']
        #dates.update(dict2)
        save_name = str(datas["dev"]) + "-" + str(datas["type"]) + save_time + "-" + str(image_id) 
        try:
            # 设置目录路径
            directory = "./data"
            # 获取当前的年月日
            today = datetime.datetime.now().strftime("%Y%m%d")
            # 构建完整的文件夹路径
            folderPath = os.path.join(directory, today)
            # 检查文件夹是否存在
            if not os.path.exists(folderPath):
                # 如果文件夹不存在，则创建它
                os.makedirs(folderPath)
            save_path = os.path.join(folderPath, save_name + ".jpg")
            print("save_path: ", save_path)
            cv2.imwrite(save_path, img)
            if img_ori is not None:
                save_path = os.path.join(folderPath, save_name + "_ori" + ".jpg")
                cv2.imwrite(save_path, img_ori)
            if img_depth is not None:
                save_path = os.path.join(folderPath, save_name + "_depth" + ".png")
                cv2.imwrite(save_path, img_depth)
            WriteDataFile = os.path.join(folderPath, 'Data.csv')
            with open(WriteDataFile, "a+", newline='') as f:
                # with open(birth_weight_file, "w") as f:
                writer = csv.writer(f)
                writer.writerow(datas.values())
                f.close()
        except Exception as e:
            print("save_csv error: {}".format(e))

def run_camera(sn,id):
    camera = CameraHandler(sn,id)
    camera.start_stream()
    try:
        while True:
            image_list = camera.get_images()
            if image_list:
                camera.process_images(image_list)
    finally:
        camera.stop_stream()
        camera.close_camera()
