# 布捆直径检测改进算法说明

## 1. 算法概述

本次改进在原有的布捆直径检测基础上，新增了**辅助判断线算法**，用于验证直径测量的准确性。当检测到的直径可能存在误差时，系统会自动显示辅助判断线，帮助测试人员进行人工验证。

## 2. 改进内容

### 2.1 原有算法
- 使用`minAreaRect`获取布捆的最小外接矩形
- 沿法线方向在3个采样点（中心、中心±50像素）测量直径
- 计算平均直径作为最终结果

### 2.2 新增算法
- **布捆两端检测**：沿长轴方向寻找布捆的两个端点
- **辅助距离计算**：计算中心点到两端连线的距离
- **偏差判断**：比较直径和辅助距离的偏差
- **智能显示**：仅在偏差超过20mm时显示辅助判断线

## 3. 技术实现

### 3.1 布捆两端检测
```python
def find_cloth_ends(center, axis_vec, mask, max_search):
    """沿着长轴方向寻找布捆的两端点"""
    # 正方向搜索端点
    # 负方向搜索端点
    # 返回两个端点坐标
```

**关键特点：**
- 使用`minAreaRect`的长轴方向作为搜索方向
- 沿mask边界寻找真正的布捆端点
- 确保搜索距离足够（至少10像素）避免噪声

### 3.2 辅助距离计算
```python
# 点到直线距离公式: |ax + by + c| / sqrt(a^2 + b^2)
a = y2 - y1
b = -(x2 - x1)  
c = (x2 - x1) * y1 - (y2 - y1) * x1
pixel_distance = abs(a * cx + b * cy + c) / sqrt(a*a + b*b)
```

**物理距离转换：**
```python
auxiliary_distance_mm = pixel_distance * depth / fx
```

### 3.3 偏差判断逻辑
```python
diameter_deviation = abs(avg_diameter - auxiliary_distance * 2)
if diameter_deviation > 20:  # 20mm阈值
    show_auxiliary_line = True
```

## 4. 视觉效果

### 4.1 正常情况（偏差≤20mm）
- 显示红色/蓝色直径测量线
- 显示直径数值
- **不显示**辅助判断线

### 4.2 异常情况（偏差>20mm）
- 显示红色/蓝色直径测量线
- **新增显示**：
  - 🟡 **黄色辅助判断线**：连接布捆两端
  - 🟢 **绿色虚线**：中心点到辅助线的垂线
  - 🟡 **偏差数值**：显示具体偏差值
  - ⚠️ **警告标记**：`⚠ DIAMETER CHECK`

## 5. 代码位置

### 5.1 主要修改文件
- `YoloOnnx.py` - 第443-559行

### 5.2 新增函数
- `draw_dashed_line()` - 绘制虚线效果
- `find_cloth_ends()` - 布捆端点检测（内嵌函数）

### 5.3 关键变量
- `auxiliary_distance_mm` - 辅助判断距离
- `show_auxiliary_line` - 是否显示辅助线
- `diameter_deviation` - 直径偏差值

## 6. 参数配置

### 6.1 可调参数
```python
DEVIATION_THRESHOLD = 20  # 偏差阈值（mm）
SAMPLE_OFFSET = 50        # 采样点偏移（像素）
DASH_LENGTH = 10          # 虚线长度（像素）
```

### 6.2 颜色配置
```python
AUXILIARY_LINE_COLOR = (0, 255, 255)    # 黄色
PERPENDICULAR_COLOR = (0, 255, 0)       # 绿色
WARNING_COLOR = (0, 255, 255)           # 黄色
```

## 7. 使用说明

### 7.1 自动触发
- 算法会自动计算每个布捆的直径偏差
- 当偏差超过20mm时自动显示辅助判断线
- 无需人工干预

### 7.2 人工判断
当看到辅助判断线时，测试人员应该：
1. 观察黄色辅助线是否正确连接布捆两端
2. 检查绿色垂线长度是否合理
3. 对比直径测量线和辅助线的差异
4. 根据实际情况判断哪个测量更准确

### 7.3 结果解读
- **无辅助线**：直径测量可信度高
- **有辅助线**：建议人工复核，可能存在以下情况：
  - 布捆形状不规则
  - 遮挡或重叠
  - 检测边界不准确
  - 深度数据异常

## 8. 测试验证

### 8.1 测试脚本
运行 `test_diameter_algorithm.py` 进行功能测试：
```bash
python test_diameter_algorithm.py
```

### 8.2 测试要点
- 验证辅助线显示逻辑
- 检查偏差计算准确性
- 确认视觉效果清晰度
- 测试各种布捆形状

## 9. 性能影响

### 9.1 计算开销
- 新增计算量：约5-10%
- 主要开销：端点搜索和距离计算
- 对实时性影响：微乎其微

### 9.2 内存使用
- 无额外内存分配
- 使用临时变量进行计算
- 不影响原有数据结构

## 10. 后续优化建议

### 10.1 算法优化
- 可考虑使用椭圆拟合提高精度
- 增加多角度采样验证
- 优化端点检测算法

### 10.2 交互优化
- 添加配置界面调整阈值
- 支持手动开启/关闭辅助线
- 增加统计报告功能

### 10.3 扩展功能
- 记录偏差历史数据
- 自动学习优化阈值
- 支持多种判断标准
