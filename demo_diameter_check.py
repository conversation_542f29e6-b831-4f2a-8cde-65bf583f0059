#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
布捆直径检测算法演示脚本
展示辅助判断线的工作原理
"""

import cv2
import numpy as np
import math

def create_demo_image():
    """
    创建演示用的布捆图像
    """
    # 创建空白图像
    img = np.zeros((600, 800, 3), dtype=np.uint8)
    img.fill(50)  # 深灰色背景
    
    # 绘制两个布捆示例
    
    # 布捆1：正常情况（直径测量准确）
    center1 = (200, 150)
    width1, height1 = 120, 60
    angle1 = 15
    
    # 布捆2：异常情况（直径测量有偏差）
    center2 = (200, 350)
    width2, height2 = 140, 50  # 不规则形状
    angle2 = -20
    
    # 绘制布捆轮廓
    def draw_cloth_bundle(img, center, width, height, angle, color):
        # 计算旋转矩形的四个顶点
        rect = ((center[0], center[1]), (width, height), angle)
        box = cv2.boxPoints(rect)
        box = np.int0(box)
        
        # 填充布捆
        cv2.fillPoly(img, [box], color)
        cv2.polylines(img, [box], True, (255, 255, 255), 2)
        
        return rect, box
    
    # 绘制两个布捆
    rect1, box1 = draw_cloth_bundle(img, center1, width1, height1, angle1, (100, 150, 200))
    rect2, box2 = draw_cloth_bundle(img, center2, width2, height2, angle2, (100, 150, 200))
    
    return img, [(rect1, box1, center1), (rect2, box2, center2)]

def simulate_diameter_detection(img, cloth_data):
    """
    模拟直径检测算法
    """
    results = []
    
    for i, (rect, box, center) in enumerate(cloth_data):
        print(f"\n=== 布捆 {i+1} 检测 ===")
        
        # 模拟直径测量
        width, height = rect[1]
        angle = rect[2]
        
        # 计算法线方向（垂直于长轴）
        if width > height:
            theta = math.radians(angle)
            diameter_measured = height  # 短边作为直径
        else:
            theta = math.radians(angle + 90)
            diameter_measured = width
        
        # 绘制直径测量线
        normal_vec = np.array([math.cos(theta), math.sin(theta)])
        line_length = diameter_measured / 2 + 10
        
        pt1 = (int(center[0] + normal_vec[0] * line_length),
               int(center[1] + normal_vec[1] * line_length))
        pt2 = (int(center[0] - normal_vec[0] * line_length),
               int(center[1] - normal_vec[1] * line_length))
        
        cv2.line(img, pt1, pt2, (0, 0, 255), 3)  # 红色直径线
        cv2.putText(img, f"D={diameter_measured:.1f}", 
                   (center[0] + 10, center[1] - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        
        # 计算布捆两端点
        if width > height:
            tangent_vec = np.array([-normal_vec[1], normal_vec[0]])
            end_distance = width / 2
        else:
            tangent_vec = np.array([normal_vec[1], -normal_vec[0]])
            end_distance = height / 2
        
        end1 = (int(center[0] + tangent_vec[0] * end_distance),
                int(center[1] + tangent_vec[1] * end_distance))
        end2 = (int(center[0] - tangent_vec[0] * end_distance),
                int(center[1] - tangent_vec[1] * end_distance))
        
        # 计算中心点到两端连线的距离（辅助判断距离）
        # 点到直线距离公式
        x1, y1 = end1
        x2, y2 = end2
        cx, cy = center
        
        a = y2 - y1
        b = -(x2 - x1)
        c = (x2 - x1) * y1 - (y2 - y1) * x1
        
        if a != 0 or b != 0:
            auxiliary_distance = abs(a * cx + b * cy + c) / math.sqrt(a*a + b*b)
        else:
            auxiliary_distance = 0
        
        # 计算偏差
        deviation = abs(diameter_measured - auxiliary_distance * 2)
        
        print(f"直径测量: {diameter_measured:.1f}")
        print(f"辅助距离*2: {auxiliary_distance*2:.1f}")
        print(f"偏差: {deviation:.1f}")
        
        # 判断是否显示辅助判断线
        if deviation > 20:  # 20像素阈值（模拟20mm）
            print("偏差超过阈值，显示辅助判断线")
            
            # 绘制辅助判断线（黄色）
            cv2.line(img, end1, end2, (0, 255, 255), 3)
            cv2.circle(img, end1, 6, (0, 255, 255), -1)
            cv2.circle(img, end2, 6, (0, 255, 255), -1)
            
            # 计算并绘制垂线
            denominator = a*a + b*b
            foot_x = cx - a * (a*cx + b*cy + c) / denominator
            foot_y = cy - b * (a*cx + b*cy + c) / denominator
            foot_point = (int(foot_x), int(foot_y))
            
            # 绘制虚线垂线
            draw_dashed_line(img, center, foot_point, (0, 255, 0), 2, 8)
            
            # 显示偏差信息
            mid_x = (end1[0] + end2[0]) // 2
            mid_y = (end1[1] + end2[1]) // 2
            cv2.putText(img, f"Dev: {deviation:.1f}", 
                       (mid_x + 10, mid_y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)
            
            # 警告标记
            cv2.putText(img, "⚠ CHECK", 
                       (center[0] + 80, center[1] - 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        else:
            print("偏差在正常范围内")
        
        results.append({
            'center': center,
            'diameter': diameter_measured,
            'auxiliary_distance': auxiliary_distance * 2,
            'deviation': deviation,
            'show_auxiliary': deviation > 20
        })
    
    return results

def draw_dashed_line(img, pt1, pt2, color, thickness, dash_length):
    """
    绘制虚线
    """
    x1, y1 = pt1
    x2, y2 = pt2
    
    dx = x2 - x1
    dy = y2 - y1
    length = math.sqrt(dx*dx + dy*dy)
    
    if length == 0:
        return
    
    unit_x = dx / length
    unit_y = dy / length
    
    current_length = 0
    draw = True
    
    while current_length < length:
        start_x = int(x1 + unit_x * current_length)
        start_y = int(y1 + unit_y * current_length)
        
        end_length = min(current_length + dash_length, length)
        end_x = int(x1 + unit_x * end_length)
        end_y = int(y1 + unit_y * end_length)
        
        if draw:
            cv2.line(img, (start_x, start_y), (end_x, end_y), color, thickness)
        
        current_length += dash_length
        draw = not draw

def add_legend(img):
    """
    添加图例说明
    """
    legend_y = 450
    
    # 标题
    cv2.putText(img, "Legend:", (500, legend_y), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 直径线
    cv2.line(img, (520, legend_y + 30), (560, legend_y + 30), (0, 0, 255), 3)
    cv2.putText(img, "Diameter Line", (570, legend_y + 35), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 辅助线
    cv2.line(img, (520, legend_y + 60), (560, legend_y + 60), (0, 255, 255), 3)
    cv2.putText(img, "Auxiliary Line", (570, legend_y + 65), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 垂线
    draw_dashed_line(img, (520, legend_y + 90), (560, legend_y + 90), (0, 255, 0), 2, 6)
    cv2.putText(img, "Perpendicular", (570, legend_y + 95), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # 说明
    cv2.putText(img, "Auxiliary line shows when", (500, legend_y + 130), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)
    cv2.putText(img, "deviation > 20mm", (500, legend_y + 150), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)

def main():
    """
    主函数
    """
    print("=== 布捆直径检测算法演示 ===")
    
    # 创建演示图像
    img, cloth_data = create_demo_image()
    
    # 模拟检测算法
    results = simulate_diameter_detection(img, cloth_data)
    
    # 添加图例
    add_legend(img)
    
    # 添加标题
    cv2.putText(img, "Cloth Bundle Diameter Detection Demo", 
               (50, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    # 显示结果
    print(f"\n=== 检测结果汇总 ===")
    for i, result in enumerate(results):
        print(f"布捆 {i+1}:")
        print(f"  直径: {result['diameter']:.1f}")
        print(f"  辅助距离*2: {result['auxiliary_distance']:.1f}")
        print(f"  偏差: {result['deviation']:.1f}")
        print(f"  显示辅助线: {'是' if result['show_auxiliary'] else '否'}")
    
    # 保存演示图像
    cv2.imwrite('diameter_detection_demo.jpg', img)
    print(f"\n演示图像已保存: diameter_detection_demo.jpg")
    
    # 显示图像（如果在支持GUI的环境中）
    try:
        cv2.imshow('Diameter Detection Demo', img)
        print("按任意键关闭窗口...")
        cv2.waitKey(0)
        cv2.destroyAllWindows()
    except:
        print("无法显示窗口（可能在无GUI环境中运行）")

if __name__ == "__main__":
    main()
