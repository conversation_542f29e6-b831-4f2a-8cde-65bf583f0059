from unittest import result
import numpy as np
import configparser
import math
import yaml
import random
from Management import ManagementConnect
import logging
from logging import handlers
import os
import time
from YoloOnnx import YOLOv8Seg
"""
函数名：get_yaml_load()
功能  加载yaml文件
"""
def get_yaml_load(filename):
    with open(filename,'r', encoding='utf-8') as fp:
        file_data = fp.read()
        fp.close()
        data = yaml.load(file_data, Loader=yaml.FullLoader)
        print(data)
        return data

"""
函数名：reparam()
功能  获取参数列表
"""
def reparam():
    global ServerHost, ServerPort
    global HeartbeatCycle, DEBUG, CameraIds,HeartTime
    global local_image_mode
    
    global box_model

    try:
        params = get_yaml_load('params.yaml')
        DEBUG = params['DEBUG']

        ServerHost = params['tcpServer']['host']#上位机IP
        ServerPort = params['tcpServer']['port']#上位机端口
        HeartbeatCycle = params['tcpServer']['heartbeat']
        CameraIds = params['camera']['id']
        local_image_mode = params.get('local_image_mode', False)  # 读取本地图片模式配置
        HeartTime = time.time()
            # 实例化模型
        model_path = "best.onnx"
        box_model = YOLOv8Seg(model_path)
    except  Exception as e:
        print("读取参数失败，请检查配置文件" + str(e.args))

class Logger(object):
    level_relations = {
        'debug':logging.DEBUG,
        'info':logging.INFO,
        'warning':logging.WARNING,
        'error':logging.ERROR,
        'crit':logging.CRITICAL
    }#日志级别关系映射

    def __init__(self,filename,level='info',when='D',backCount=3,fmt='【%(asctime)s】  %(message)s'):#- %(pathname)s[line:%(lineno)d] - %(levelname)s:
        self.logger = logging.getLogger(filename)
        format_str = logging.Formatter(fmt)#设置日志格式
        self.logger.setLevel(self.level_relations.get(level))#设置日志级别
        sh = logging.StreamHandler()#往屏幕上输出
        sh.setFormatter(format_str) #设置屏幕上显示的格式
        th = handlers.TimedRotatingFileHandler(filename=filename,when=when,backupCount=backCount,encoding='utf-8')#往文件里写入#指定间隔时间自动生成文件的处理器
        #实例化TimedRotatingFileHandler
        #interval是时间间隔，backupCount是备份文件的个数，如果超过这个个数，就会自动删除，when是间隔的时间单位，单位有以下几种：
        # S 秒
        # M 分
        # H 小时、
        # D 天、
        # W 每星期（interval==0时代表星期一）
        # midnight 每天凌晨
        th.setFormatter(format_str)#设置文件里写入的格式
        self.logger.addHandler(sh) #把对象加到logger里
        self.logger.addHandler(th)

def loginit():
    global log
    basedir = os.path.abspath(os.path.dirname(__file__))
    log_path = os.path.join(basedir, 'logs' )  # 日志根目录 ../logs/
    if not os.path.exists(log_path):
        os.mkdir(log_path)
    log_filename = time.strftime("%F") + '.log'
    log_name = os.path.join(log_path,log_filename)
    log = Logger(log_name,level='debug')


def recv_msg(dev, type, taskNum, task):
    msg = {}
    msg['eid'] = 'VISUAL'
    msg['dev'] = dev
    msg['type'] = type
    msg['taskNum'] = taskNum
    
    return msg

# 添加测试模式配置
TEST_SW_STRING = "PLACEMENT"  # "BOX"   "QR"  "PLACEMENT"

# 托盘参数配置
pallet_configs = {
    "DC1": {
        "center": {
            "x": 300,
            "y": 50,
            "z": 2500,
            "l": 1920,
            "w": 1360,
            "h": 3990
        },
        "detect_range": {
            "x": [-700, 1300],
            "y": [-780, 880],
            "z": [2300, 4140]
        }
    },
    "DC2": {
        "center": {
            "x": 160,
            "y": 130,
            "z": 2230,
            "l": 1920,
            "w": 1360,
            "h": 3990
        },
        "detect_range": {
            "x": [-1100, 1420],
            "y": [-700, 960],
            "z": [2080, 4140]
        }
    },
    "DC3": {
        "center": {
            "x": 800,      # 向右移动到800
            "y": -200,     # 向上移动到-200
            "z": 2500,     # 保持高度不变
            "l": 1920,     # 托盘长度
            "w": 1360,     # 托盘宽度
            "h": 2000      # 保持高度不变
        },
        "detect_range": {
            "x": [-200, 1800],  # 调整检测范围以匹配新的中心位置
            "y": [-880, 480],   # 调整Y范围以匹配新的中心位置
            "z": [2300, 3000]   # 保持Z范围不变
        }
    }
}