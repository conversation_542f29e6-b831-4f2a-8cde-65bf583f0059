from sys import byteorder
import numpy as np
import cv2
import math
from _socket import *
import configparser
from struct import pack , unpack
import threading
from threading import Thread ,Timer
import queue
import datetime
import time
import yaml
import inspect
import config
import traceback
import Management
from Management import ManagementConnect
import frame_get
from frame_get import CameraHandler
from frame_get_d435 import D435CameraHandler
from YoloOnnx import YOLOv8Seg

# 识别测试模式
TEST_SW_BOOL = True   # True    False
TEST_SW_STRING = "PLACEMENT" # "BOX"   "QR"  "PLACEMENT"
TEST_SW_DEV = "DC1" #DC1 DC2 DC3

"""
函数名:ReadSendId()  
判断是否有数据需要发送
"""
def ReadSendId():
    now = time.time()
    try:
        result = manageConn.result_queue.get_nowait()
    except queue.Empty:
        result = None
    if result is not None:
        if result['type'] == "QRCodeFeedback":
            manageConn.SendMsgQR(result)
        else:
            manageConn.SendMsg(result)
        
    elif abs(now - config.HeartTime) >= config.HeartbeatCycle:
        #print('hbtime:',time.time() - config.HeartTime)
        manageConn.HeartBeat()
        #manageConn.StatusReport()
        config.HeartTime = time.time()
        return 0
    return 0
def test_msg():
    if TEST_SW_STRING == "BOX":
        msg = {"eid": "VISUAL","dev":TEST_SW_DEV, "type": "box", "taskNum": "box20240305000001","task":{"time": "30"},"sendTime": "2024-03-05 16:46:55"}
    elif TEST_SW_STRING == "PLACEMENT":
        msg = {"eid": "VISUAL","dev":TEST_SW_DEV, "type": "placementLocation", "taskNum": "placementLocation20240305000001","task":{"length": 1900,"width": 250,"height": 280},"sendTime": "2024-03-05 16:46:55"}
    else:
        msg = {'eid': 'VISUAL', 'dev':TEST_SW_DEV, 'type': 'QRCode', 'taskNum': 'QRCode20240305000001', 'task': {'Number': '146'}, 'sendTime': '2024-03-05 16:46:55'}
    
    print(f"\n[ROBOT调试] ====== test_msg开始 ======")
    print(f"[ROBOT调试] 准备发送任务: {msg['type']} 到设备 {msg['dev']}")
    print(f"[ROBOT调试] 当前相机状态: isbusy={cameras[TEST_SW_DEV].isbusy}, start={cameras[TEST_SW_DEV].start}")
    
    while cameras[TEST_SW_DEV].isbusy:
        time.sleep(0.1)
        print(f"[ROBOT调试] 等待相机 {TEST_SW_DEV} 空闲... (isbusy={cameras[TEST_SW_DEV].isbusy})")
    
    print(f"[ROBOT调试] 相机 {TEST_SW_DEV} 空闲，开始执行任务")
    print(f"[ROBOT调试] 调用run_task前的状态: isbusy={cameras[TEST_SW_DEV].isbusy}, start={cameras[TEST_SW_DEV].start}")
    success = cameras[TEST_SW_DEV].run_task(msg)
    print(f"[ROBOT调试] run_task返回结果: {success}")
    print(f"[ROBOT调试] 调用run_task后的状态: isbusy={cameras[TEST_SW_DEV].isbusy}, start={cameras[TEST_SW_DEV].start}")
    print(f"[ROBOT调试] ====== test_msg结束 ======\n")
    


"""
函数名:MainRunmanageConn()  
连接上位机
"""
def MainRunManageConn(cameras,result_queue):
    global manageConn
    print(f"[ROBOT调试] 连接到上位机: {config.ServerHost}:{config.ServerPort}")
    manageConn = ManagementConnect(config.ServerHost, config.ServerPort, cameras, result_queue)
    manageConn.start()
    manageConn.Parametersinit()
    print(f"[ROBOT调试] 上位机连接成功，开始主循环")
    while True:
        time.sleep(0.01)
        if TEST_SW_BOOL:
            test_msg()
        try:
            PackageId = ReadSendId()
            #SendPackage(PackageId);
            time.sleep(0.1)
        except Exception as e:
            print("MainRun Error:" + str(e.args))
"""
函数名:RealsenseRun()
Realsense主函数
"""
def RealsenseRun():
    
    while True:
        try:
            time.sleep(0.01)
        except Exception as e:
            config.log.logger.info("RealsenseRun Error:" + str(e.args))
            cv2.destroyAllWindows()

def main():
    try:
        # 读取配置
        config.reparam()
        # 初始化LOG
        config.loginit()
    except Exception as e:
        print("Main Error reparam:" + str(e.args))

    # 从params.yaml读取local_image_mode配置
    LOCAL_IMAGE_MODE = config.local_image_mode

    result_queue = queue.Queue()
    global cameras
    cameras = {
        "DC1": CameraHandler(config.CameraIds[0], "DC1", result_queue),
        "DC2": CameraHandler(config.CameraIds[1], "DC2", result_queue),
        "DC3": CameraHandler(config.CameraIds[2], "DC3", result_queue),
        "DC0": D435CameraHandler("d435", "DC0", result_queue)
    }

    print(cameras["DC0"].offline)

    for camera in cameras.values():
        if camera.offline:
            continue
        Thread(target=camera.run_camera_in_main_thread).start()
    print("start")
    MainRunManageConn(cameras, result_queue)


if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        config.log.logger.info("Main Error:" + str(e.args))