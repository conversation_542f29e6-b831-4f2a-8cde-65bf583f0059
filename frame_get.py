import pcammls
from pcammls import * 
import cv2
import numpy
import sys
import os
import datetime
import time
import csv
import threading
from threading import Thread ,Timer
import logging
import config
#import pupil_apriltags as apriltag
import random
import math
import numpy as np
import queue
import YoloOnnx
import copy
import open3d as o3d
from YoloOnnx import YOLOv8Seg
import yaml  # 添加yaml导入
import traceback  # 添加traceback导入

QRCODE = "CV2"

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

'''
托盘尺寸： 外 200*140 内 192*136

'''


# 定义一个继承自pcammls.DeviceEvent的类
class PythonPercipioDeviceEvent(pcammls.DeviceEvent):
    # 定义一个Offline属性，默认为False
    Offline = False

    # 初始化函数
    def __init__(self):
        pcammls.DeviceEvent.__init__(self)

    # 运行函数，当设备离线时，打印提示信息，并将Offline属性设置为True
    def run(self, handle, eventID):
        if eventID==TY_EVENT_DEVICE_OFFLINE:
            print('=== Event Callback: Device Offline!')
            self.Offline = True
        return 0

    # 返回Offline属性的值
    def IsOffline(self):
        return self.Offline
    
class CameraHandler:
    def __init__(self, sn, id, result_queue, use_local_image=False, local_image_folder=None):
        self.sn = sn
        self.id = id
        self.cl = PercipioSDK()
        self.handle = self.cl.Open(sn)
        self.offline = False
        self.result_queue = result_queue
        self.isbusy = False
        self.task = None
        self.start = False
        self.msg = None
        self.depth_calib_intr = None
        self.img_w = 2560
        self.img_h = 1920
        self.start_time = time.time()
        self.result_list = []
        self.frame_count = 0
        self.MAX_FRAME_COUNT = 10
        self.image_id = 0
        self.pallet_center = {"x":0, "y":0, "z":0, "angle":0, "l":1820, "w":1160, "h":4300}
        self.use_local_image = use_local_image
        self.local_image_folder = local_image_folder
        if use_local_image and local_image_folder:
            # 创建输出文件夹
            output_folder = os.path.join(os.path.dirname(local_image_folder), "processed_results")
            if not os.path.exists(output_folder):
                os.makedirs(output_folder)
                print(f"[调试] 创建输出文件夹: {output_folder}")
            self.output_folder = output_folder
            
            # 获取所有.ori.jpg文件，处理文件名中的空格问题
            all_files = os.listdir(local_image_folder)
            
            self.image_files = []
            for f in all_files:
                f_lower = f.lower()
                # 使用更宽松的匹配条件
                if f_lower.endswith('.ori.jpg') or 'ori.jpg' in f_lower:
                    self.image_files.append(f)
            
            self.image_files = sorted(self.image_files)
            print(f"[调试] 本地图片文件列表: {self.image_files}")
            print(f"[调试] 找到 {len(self.image_files)} 个.ori.jpg文件")
            self.image_index = 0
        
        if self.id =="DC1":
            self.pallet_center = {"x":-300, "y":-25, "z":2230, "angle":0, "l":1800, "w":1240, "h":3990}#{"x":0, "y":0, "z":2250, "angle":0, "l":1820, "w":1160, "h":3990}
        elif self.id =="DC2":
            self.pallet_center = {"x":160, "y":130, "z":2230, "angle":0, "l":1800, "w":1240, "h":3990}
        elif self.id =="DC3":
            self.pallet_center = {"x":-10, "y":-80, "z":2230, "angle":0, "l":1800, "w":1240, "h":3990}
        self.xyz_range = (self.pallet_center['x'] - self.pallet_center['l'] / 2 - 300, self.pallet_center['x'] + self.pallet_center['l'] / 2 + 300,
                          self.pallet_center['y'] - self.pallet_center['w'] / 2 - 150, self.pallet_center['y'] + self.pallet_center['w'] / 2 + 150,
                          self.pallet_center['z'] - 150, self.pallet_center['h'] + 150)
        if QRCODE == "CV2":
            self.detector = cv2.aruco.getPredefinedDictionary(cv2.aruco.DICT_APRILTAG_36H11)
        else:
            self.detector = apriltag.Detector(families='tag36h11')
        
        if not self.cl.isValidHandle(self.handle):
            err = self.cl.TYGetLastErrorCodedescription()
            print(f'相机 {self.id} 连接失败: {err}')
            self.offline = True
        else:
            print(f'相机 {self.id} 连接成功')
            self.event = PythonPercipioDeviceEvent()
            self.cl.DeviceRegiststerCallBackEvent(self.event)
            self.init_camera_settings()

            roi = PercipioAecROI(238*2, 175*2, 1140*2, 795*2)
            param =self.cl.DevParamFromPercipioAecROI(roi)
            self.cl.DeviceSetParameter(self.handle, TY_COMPONENT_RGB_CAM, TY_STRUCT_AEC_ROI, param)
            read_param = self.cl.DeviceGetParameter(self.handle, TY_COMPONENT_RGB_CAM, TY_STRUCT_AEC_ROI)
            m_read_param=read_param.toArray()
            print(f"m_read_param = {m_read_param}")

    def load_default_params(self):
        depth_fmt_list = self.cl.DeviceStreamFormatList(self.handle, PERCIPIO_STREAM_DEPTH)
        logging.info("Depth format list: %s", depth_fmt_list)
        # 配置深度图像格式
        self.cl.DeviceStreamFormatConfig(self.handle, PERCIPIO_STREAM_DEPTH, depth_fmt_list[1])
        # 加载默认参数
        err = self.cl.DeviceLoadDefaultParameters(self.handle)
        if err:
            logging.error('Load default parameters fail: %s', self.cl.TYGetLastErrorCodedescription())
        else:
            logging.info('Load default parameters successful')

        # 读取深度图像的标定尺度单位
        self.scale_unit = self.cl.DeviceReadCalibDepthScaleUnit(self.handle)
        logging.info('depth image scale unit :%s', self.scale_unit)
        # 读取深度图像和颜色图像的标定数据
        self.depth_calib = self.cl.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_DEPTH)
        self.color_calib = self.cl.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_COLOR)

    def depth2xyz(self, px, py, depth):
        cx = self.depth_calib_intr[2]#663.191772
        cy = self.depth_calib_intr[5]#494.933533
        fx = self.depth_calib_intr[0]#1044.018799
        fy = self.depth_calib_intr[4]#1044.018799
        z = int(depth)
        x = int((px - cx) * z / fx)
        y = int((py - cy) * z / fy)
        result = [x, y, z]
        return result

    def xyz2depth(self, x, y, z):
        cx = self.depth_calib_intr[2]
        cy = self.depth_calib_intr[5]
        fx = self.depth_calib_intr[0]
        fy = self.depth_calib_intr[4]
        px = int(x * fx / z + cx)
        py = int(y * fy / z + cy)
        result = [px, py]
        return result

    def init_camera_settings(self,color_fmt_sw = 0 ):
        # 获取颜色图像格式列表
        color_fmt_list = self.cl.DeviceStreamFormatDump(self.handle, PERCIPIO_STREAM_COLOR)
        if len(color_fmt_list) == 0:
            print ('device has no color stream.')
            return
        # 打印颜色图像格式列表
        print ('color image format list:')
        for idx in range(len(color_fmt_list)):
            fmt = color_fmt_list[idx]
            print ('\t{} -size[{}x{}]\t-\t desc:{}'.format(idx, self.cl.Width(fmt), self.cl.Height(fmt), fmt.getDesc()))
        """
            color image format list:
                0 -size[2560x1920]      -        desc:yuyv 2560x1920
                1 -size[1920x1440]      -        desc:yuyv 1920x1440
                2 -size[1280x960]       -        desc:yuyv 1280x960
                3 -size[640x480]        -        desc:yuyv 640x480
                4 -size[2560x1920]      -        desc:CSI BAYER12GR_2560x1920
        """
        # 配置颜色图像格式
        self.cl.DeviceStreamFormatConfig(self.handle, PERCIPIO_STREAM_COLOR, color_fmt_list[color_fmt_sw])

        # 获取深度图像格式列表
        depth_fmt_list = self.cl.DeviceStreamFormatDump(self.handle, PERCIPIO_STREAM_DEPTH)
        # 如果深度图像格式列表为空，打印提示信息并返回
        if len(depth_fmt_list) == 0:
            print ('device has no depth stream.')
            return

        # 打印深度图像格式列表
        print ('depth image format list:')
        for idx in range(len(depth_fmt_list)):
            fmt = depth_fmt_list[idx]
            print ('\t{} -size[{}x{}]\t-\t desc:{}'.format(idx, self.cl.Width(fmt), self.cl.Height(fmt), fmt.getDesc()))
        """
            depth image format list:
                0 -size[640x480]        -        desc:DEPTH16_640x480
                1 -size[1280x960]       -        desc:DEPTH16_1280x960
                2 -size[320x240]        -        desc:DEPTH16_320x240
        """
        # 配置深度图像格式
        self.cl.DeviceStreamFormatConfig(self.handle, PERCIPIO_STREAM_DEPTH, depth_fmt_list[1])
        # 加载默认参数
        err = self.cl.DeviceLoadDefaultParameters(self.handle)
        # 如果加载失败，打印错误信息
        if err:
            logging.error('Load default parameters fail: %s', self.cl.TYGetLastErrorCodedescription())
        else:
            logging.info('Load default parameters successful')
        
        # 读取深度图像的标定尺度单位
        self.scale_unit = self.cl.DeviceReadCalibDepthScaleUnit(self.handle)
        print ('depth image scale unit :{}'.format(self.scale_unit))
        # 读取深度图像和颜色图像的标定数据
        self.depth_calib = self.cl.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_DEPTH)
        self.color_calib = self.cl.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_COLOR)
        depth_calib_width  = self.depth_calib.Width()
        depth_calib_height = self.depth_calib.Height()
        #self.img_w = depth_calib_width
        #self.img_h = depth_calib_height
        self.depth_calib_intr   = self.color_calib.Intrinsic()
        depth_calib_extr   = self.depth_calib.Extrinsic()
        depth_calib_dis    = self.depth_calib.Distortion()
        print('delth calib info:')
        print('\tcalib size       :[{}x{}]'.format(depth_calib_width, depth_calib_height))
        print('\tcalib intr       : {}'.format(self.depth_calib_intr))
        print('\tcalib extr       : {}'.format(depth_calib_extr))
        print('\tcalib distortion : {}'.format(depth_calib_dis))
        color_calib_width  = self.color_calib.Width()
        color_calib_height = self.color_calib.Height()
        color_calib_intr   = self.color_calib.Intrinsic()
        color_calib_extr   = self.color_calib.Extrinsic()
        color_calib_dis    = self.color_calib.Distortion()
        self.img_w = color_calib_width
        self.img_h = color_calib_height
        self.depth_calib_intr   = self.color_calib.Intrinsic()
        print('color calib info:')
        print('\tcalib size       :[{}x{}]'.format(color_calib_width, color_calib_height))
        print('\tcalib intr       : {}'.format(color_calib_intr))
        print('\tcalib extr       : {}'.format(color_calib_extr))
        print('\tcalib distortion : {}'.format(color_calib_dis))
        # 新增：打印当前内参
        print(f"[调试] 当前相机内参 self.depth_calib_intr: {self.depth_calib_intr}")
        pass 

    def start_stream(self):
        try:
            err = self.cl.DeviceStreamEnable(self.handle, PERCIPIO_STREAM_COLOR | PERCIPIO_STREAM_DEPTH)
            if err:
                logging.error('device stream enable err: %s', err)
                return False
            self.cl.DeviceStreamOn(self.handle)
            return True
        except Exception as e:
            logging.error("Error starting stream: %s", e)
            return False

    def get_images(self):
        if self.use_local_image and self.local_image_folder:
            if self.image_index >= len(self.image_files):
                print('本地图片已全部处理完毕')
                return None
            img_path = os.path.join(self.local_image_folder, self.image_files[self.image_index])
            print(f'准备处理图片: {img_path}')
            
            # 使用绝对路径并处理中文路径问题
            img_path_abs = os.path.abspath(img_path)
            print(f'绝对路径: {img_path_abs}')
            img = cv2.imdecode(np.fromfile(img_path_abs, dtype=np.uint8), cv2.IMREAD_COLOR)
            
            # 查找对应的深度图（将_ori.jpg替换为_depth.png）
            original_filename = self.image_files[self.image_index]
            print(f'[调试] 原始文件名: {original_filename}')
            
            # 将_ori.jpg替换为_depth.png
            if original_filename.endswith('_ori.jpg'):
                depth_filename = original_filename.replace('_ori.jpg', '_depth.png')
            else:
                depth_filename = original_filename
            print(f'[调试] 深度图文件名: {depth_filename}')
            depth_path = os.path.join(self.local_image_folder, depth_filename)
            depth_path_abs = os.path.abspath(depth_path)
            print(f'深度图路径: {depth_path_abs}')
            
            if os.path.exists(depth_path_abs):
                depthimg = cv2.imdecode(np.fromfile(depth_path_abs, dtype=np.uint8), cv2.IMREAD_UNCHANGED)
                print(f'找到深度图: {depth_path_abs}')
            else:
                depthimg = None
                print(f'未找到深度图: {depth_path_abs}')
            self.image_index += 1
            if img is not None:
                print(f'图片读取成功: {img_path}')
                class DummyFrame:
                    def __init__(self, img, depthimg):
                        self.streamID = PERCIPIO_STREAM_COLOR
                        self.img = img
                        self.depthimg = depthimg
                return [DummyFrame(img, depthimg)]
            else:
                print(f'图片读取失败: {img_path}')
                return None
        else:
            try:
                if self.event.IsOffline():
                    return None
                image_list = self.cl.DeviceStreamRead(self.handle, 5000)
                if len(image_list) == 2:
                    return image_list
                return None
            except Exception as e:
                logging.error("Error getting images: %s", e)
                return None

    def resize_img(self, img, window_name,scale_factor = 0.25):           # 计算新的尺寸
            new_width = int(img.shape[1] * scale_factor)
            new_height = int(img.shape[0] * scale_factor)
            # 缩放图片
            resized_img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)
            cv2.imshow(window_name, resized_img)
            cv2.waitKey(1)

    def process_images(self, image_list):
        print(f"\n[调试] ====== process_images被调用 ======")
        print(f"[调试] 当前状态: self.start={self.start}, self.isbusy={self.isbusy}")
        print(f"[调试] image_list类型: {type(image_list)}, 长度: {len(image_list) if image_list else 0}")
        # 新增：每次处理时打印内参
        print(f"[调试] 当前使用的相机内参 self.depth_calib_intr: {self.depth_calib_intr}")
        if image_list:
            for i, frame in enumerate(image_list):
                print(f"[调试] frame {i}: 类型={type(frame)}, 属性={[attr for attr in dir(frame) if not attr.startswith('_')]}")
                if hasattr(frame, 'streamID'):
                    print(f"[调试] frame {i} streamID: {frame.streamID}")
                if hasattr(frame, 'img'):
                    print(f"[调试] frame {i} img形状: {frame.img.shape if frame.img is not None else 'None'}")
                if hasattr(frame, 'depthimg'):
                    print(f"[调试] frame {i} depthimg形状: {frame.depthimg.shape if frame.depthimg is not None else 'None'}")
        
        print('调用 process_images')
        try:
            # 本地图片模式下：推理、画框、保存（不弹窗）
            if self.use_local_image and self.local_image_folder:
                for idx, frame in enumerate(image_list):
                    print('开始模型推理')
                    boxes, segments, _ = config.box_model(frame.img, conf_threshold=0.35, iou_threshold=0.45)
                    print(f'模型推理完成，boxes数量: {len(boxes)}')
                    if len(boxes) > 0:
                        # 传递相机读取到的内参，避免NoneType错误
                        # default_depth_calib_intr = [1853.0103759765625, 0, 1257.3001708984375, 0, 1852.953369140625, 939.3478393554688, 0, 0, 1]
                        # 先获取检测结果（不画图）
                        temp_output, result = config.box_model.draw_and_visualize(
                            frame.img, boxes, segments, frame.depthimg, self.depth_calib_intr, vis=False, save=False
                        )
                        
                        # 应用智能抓取排序算法
                        if result and len(result) > 0:
                            for i, box_result in enumerate(result):
                                x, y, z, angle, px, py, w, l = box_result
                                print(f"[直径调试] Box {i}: 像素直径w={w}, 像素长度l={l}, px={px}, py={py}, z={z}")
                                fx = self.depth_calib_intr[0]
                                fy = self.depth_calib_intr[4]
                                cx = self.depth_calib_intr[2]
                                cy = self.depth_calib_intr[5]
                                # 计算物理直径（如果有换算）
                                # 以水平方向为例
                                x1 = px - w/2
                                x2 = px + w/2
                                X1 = (x1 - cx) * z / fx
                                X2 = (x2 - cx) * z / fx
                                diameter_mm = abs(X2 - X1)
                                print(f"[直径调试] 内参: fx={fx}, cx={cx}, z={z}")
                                print(f"[直径调试] 计算公式: X = (px - cx) * z / fx")
                                print(f"[直径调试] X1=({x1}-{cx})*{z}/{fx}={X1}, X2=({x2}-{cx})*{z}/{fx}={X2}")
                                print(f"[直径调试] 物理直径: {diameter_mm} mm")
                                print(f"[直径调试] 物理直径/像素直径: {diameter_mm}/{w}")
                            
                            # 应用排序算法
                            sorted_boxes = self.sort_boxes_for_grasping(boxes_for_sorting)
                            grasping_info = self.get_grasping_order_info(sorted_boxes)
                            
                            # 创建抓取顺序映射，显示最高层的所有箱子顺序
                            grasping_order_map = {}
                            
                            # 检查是否只有一层
                            if len(result) == len(grasping_info):
                                # 只有一层：显示所有箱子的抓取顺序
                                
                                for info in grasping_info:
                                    # 根据位置信息匹配原始检测结果
                                    for i, original_box in enumerate(result):
                                        if not isinstance(original_box, (list, tuple)):
                                            print(f"警告：original_box类型异常，跳过：{original_box}")
                                            continue
                                        if (abs(original_box[0] - info['position'][0]) < 50 and 
                                            abs(original_box[1] - info['position'][1]) < 50 and 
                                            abs(original_box[2] - info['position'][2]) < 50):
                                            grasping_order_map[i] = info['order']
                                           
                                            break
                            else:
                                # 多层结构：只显示最高层的抓取顺序
                               
                                for info in grasping_info:
                                    # 根据位置信息匹配原始检测结果
                                    for i, original_box in enumerate(result):
                                        if not isinstance(original_box, (list, tuple)):
                                            print(f"警告：original_box类型异常，跳过：{original_box}")
                                            continue
                                        if (abs(original_box[0] - info['position'][0]) < 50 and 
                                            abs(original_box[1] - info['position'][1]) < 50 and 
                                            abs(original_box[2] - info['position'][2]) < 50):
                                            grasping_order_map[i] = info['order']
                                           
                                            break
                            

                            
                            # 重新画图，包含抓取顺序
                            output_image, result = config.box_model.draw_and_visualize(
                                frame.img, boxes, segments, frame.depthimg, self.depth_calib_intr, 
                                vis=False, save=False, grasping_order_map=grasping_order_map
                            )
                            
                            
                            
                            if sorted_boxes:
                                best_box = sorted_boxes[0]
                                
                                
                        else:
                            output_image, result = config.box_model.draw_and_visualize(
                                frame.img, boxes, segments, frame.depthimg, self.depth_calib_intr, vis=False, save=False
                            )
                        
                        
                    else:
                        output_image = frame.img
                      
                        result = None
                    
                    # 生成输出文件名
                    original_filename = self.image_files[self.image_index - 1]  # 因为image_index已经递增了
                    base_name = original_filename.replace('.ori.jpg', '')
                    output_filename = f"{base_name}_result.jpg"
                    save_path = os.path.join(self.output_folder, output_filename)
                    save_path_abs = os.path.abspath(save_path)
                    
                    # 使用imencode处理中文路径保存问题
                    success, buffer = cv2.imencode('.jpg', output_image)
                    if success:
                        with open(save_path_abs, 'wb') as f:
                            f.write(buffer)
                        print(f"已保存识别结果: {save_path_abs}")
                    else:
                        print(f"保存失败: {save_path_abs}")
                    
                    # 本地图片模式下不显示弹窗，只保存结果
                    print("保存识别结果图像...")
                    
                    # 保存csv报告
                    datas = {
                        "dev": self.id,
                        "type": "box",
                        "result": result,
                        "image_id": self.image_index,
                        "local_image_path": save_path
                    }
                    self.save_csv(output_image, datas, self.image_index, self.start_time)
                return
            
            
            
            img_depth = None
            img_color = None
            
            for i in range(len(image_list)):
                frame = image_list[i]
                print(f"[相机模式调试] 处理frame {i}: streamID={frame.streamID}")
                if frame.streamID == PERCIPIO_STREAM_DEPTH:
                    img_depth = frame
                    print(f"[相机模式调试] 找到深度图像")
                if frame.streamID == PERCIPIO_STREAM_COLOR:
                    img_color = frame
                    print(f"[相机模式调试] 找到彩色图像")

            if img_depth is None or img_color is None:
                logging.warning("Missing depth or color image")
                print(f"[相机模式调试] 缺少深度或彩色图像: img_depth={img_depth is not None}, img_color={img_color is not None}")
                return None, None, None

            img_registration_depth = image_data()
            img_registration_render = image_data()
            img_parsed_color = image_data()
            img_undistortion_color = image_data()

            self.cl.DeviceStreamMapDepthImageToColorCoordinate(self.depth_calib, img_depth.width, img_depth.height, self.scale_unit, img_depth, self.color_calib, img_color.width, img_color.height, img_registration_depth)
            self.cl.DeviceStreamDepthRender(img_registration_depth, img_registration_render)
            np_img_registration_depth = img_registration_depth.as_nparray()
            mat_depth_render = img_registration_render.as_nparray()

            self.cl.DeviceStreamImageDecode(img_color, img_parsed_color)
            self.cl.DeviceStreamDoUndistortion(self.color_calib, img_parsed_color, img_undistortion_color)
            mat_undistortion_color = img_undistortion_color.as_nparray()

            if mat_undistortion_color.shape[:2] != mat_depth_render.shape[:2]:
                mat_depth_render = cv2.resize(mat_depth_render, (mat_undistortion_color.shape[1], mat_undistortion_color.shape[0]))

            if len(mat_undistortion_color.shape) == 2:
                mat_undistortion_color = cv2.cvtColor(mat_undistortion_color, cv2.COLOR_GRAY2BGR)
                mat_overlay = cv2.addWeighted(mat_undistortion_color, 0.5, mat_depth_render, 0.5, 0)
            elif len(mat_depth_render.shape) == 2:
                mat_depth_render_rgb = cv2.cvtColor(mat_depth_render, cv2.COLOR_GRAY2BGR)
                mat_overlay = cv2.addWeighted(mat_undistortion_color, 0.5, mat_depth_render_rgb, 0.5, 0)
            else:
                mat_overlay = cv2.addWeighted(mat_undistortion_color, 0.5, mat_depth_render, 0.5, 0)
            self.resize_img(mat_overlay, 'overlay-' + self.id)

            # 绘制托盘内部边界框（使用实际托盘尺寸，逆时针旋转1.5度）
            draw_img = copy.deepcopy(mat_undistortion_color)
            self.draw_rotated_rect(draw_img, self.pallet_center["x"], self.pallet_center["y"], self.pallet_center["z"],
                                  self.pallet_center["l"], self.pallet_center["w"], -1.5, (0, 0, 255), 2)  # 逆时针旋转1.5度
            self.resize_img(draw_img, 'mat_undistortion_color-' + self.id)

            # 在mat_overlay生成后添加托盘内部边框绘制
            draw_img = copy.deepcopy(mat_undistortion_color)
            self.draw_rotated_rect(draw_img, self.pallet_center["x"], self.pallet_center["y"], self.pallet_center["z"],
                                  self.pallet_center["l"], self.pallet_center["w"], -1.5, (0, 0, 255), 2)  # 逆时针旋转1.5度
            self.resize_img(draw_img, 'pallet_outline-' + self.id)

            # 根据任务类型绘制不同的边框
            if hasattr(self, 'task') and self.task:
                if self.task.get("type") == "placementLocation":
                    # 放置任务：使用外部大框（固定尺寸2000x1400）
                    tl_pt_placement, br_pt_placement = self.get_box_draw_rect(self.pallet_center["x"], self.pallet_center["y"], self.pallet_center["z"], 2000, 1400)
                    cv2.rectangle(mat_undistortion_color, (tl_pt_placement[0], tl_pt_placement[1]), (br_pt_placement[0], br_pt_placement[1]), (0, 0, 255), 2)
                elif self.task.get("type") == "box":
                    # 抓取任务：使用内部边框（实际托盘尺寸，逆时针旋转1.5度）
                    self.draw_rotated_rect(mat_undistortion_color, self.pallet_center["x"], self.pallet_center["y"], self.pallet_center["z"],
                                          self.pallet_center["l"], self.pallet_center["w"], -1.5, (0, 0, 255), 2)
            
         
            if self.start:
                
                now = time.time()
                if abs(now - self.start_time) > 10:
                   
                    self.msg["data"]["result"] = "fail"
                    self.msg["data"]["codelist"] = [1]
                    self.msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                    self.result_queue.put(self.msg)
                    self.start = False
                    self.isbusy = False
                 
                elif np_img_registration_depth is not None and mat_undistortion_color is not None and mat_overlay is not None:
                   
                    self.frame_count += 1
                 
                    if self.frame_count >= self.MAX_FRAME_COUNT:
                       
                        self.msg["data"]["result"] = "fail"
                        self.msg["data"]["codelist"] = [1]
                        self.msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                        self.result_queue.put(self.msg)
                        self.start = False
                        self.isbusy = False
                        
                    else:
                        save_img = copy.deepcopy(mat_undistortion_color)
            
                        ret,result = self.get_img_result(np_img_registration_depth,mat_undistortion_color)
              
                        if ret:
                            self.result_list.append(result)
                      
                            if len(self.result_list) >= 2:
                            
                                ret, result_msg = self.result_processing(self.result_list,mat_undistortion_color)
                                if ret:
                                 
                                    self.image_id += 1
                                    # --- 修复: 确保result_msg有dev字段 ---
                                    if 'dev' not in result_msg:
                                        result_msg['dev'] = self.id
                                    self.save_csv(mat_undistortion_color, result_msg, self.image_id, self.start_time,img_ori = save_img,img_depth = np_img_registration_depth)
                                    result_msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                                    self.result_queue.put(result_msg)
                                    self.start = False
                                    self.isbusy = False
                                
                                    if self.image_id >= 10:
                                        self.image_id = 0
                                else:
                               
                                    self.msg["data"]["result"] = "fail"
                                    self.msg["data"]["codelist"] = [1]
                                    self.msg["sendTime"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                                    self.result_queue.put(self.msg)
                                    self.start = False
                                    self.isbusy = False
                       
                        else:
                            print(f"[相机模式调试] get_img_result失败，继续等待下一帧")
                else:
                    print(f"[相机模式调试] 图像数据无效，跳过本次处理")
            else:
                print(f"[相机模式调试] self.start为False，跳过识别流程，只显示overlay")
        except Exception as e:
            logging.error("Error processing images: %s", e)
            return None, None, None

    def stop_stream(self):
        try:
            self.cl.DeviceStreamOff(self.handle)
        except Exception as e:
            config.log.logger.error("Error stopping stream: %s", e)

    def close_camera(self):
        try:
            self.cl.Close(self.handle)
        except Exception as e:
            config.log.logger.error("Error closing camera: %s", e)

    def run_task(self, msg):
        print(f"\n[调试] ====== run_task被调用 ======")
        print(f"[调试] 当前状态: isbusy={self.isbusy}, start={self.start}")
        print(f"[调试] 接收到的msg: {msg}")
        print(f"[调试] msg类型检查: type={msg.get('type', 'None')}, dev={msg.get('dev', 'None')}")
        
        if self.isbusy:
            print(f"[调试] 相机正忙，拒绝任务")
            config.log.logger.warning("Camera is busy, cannot run task")
            return False
        else:
            print(f"[调试] 相机空闲，开始处理任务")
            ret, self.msg = self.get_msg(msg)
            print(f"[调试] get_msg返回: ret={ret}, msg={self.msg}")
            if ret:
                print(f"[调试] get_msg成功，设置任务状态")
                self.start_time = time.time()
                self.result_list = []
                self.frame_count = 0
                self.isbusy = True
                self.start = True
                self.task = msg
                print(f"[调试] 任务状态已设置: isbusy={self.isbusy}, start={self.start}")
                print(f"[调试] ====== run_task成功结束 ======\n")
                return ret
            else:
                print(f"[调试] get_msg失败，不设置任务状态")
                print(f"[调试] ====== run_task失败结束 ======\n")
                return False

    def get_msg(self, msg):
        try:
            print(f"\n[调试] ====== get_msg开始 ======")
            print(f"[调试] 接收到的msg: {msg}")
            print(f"[调试] msg类型: {msg.get('type', 'None')}")
            
            result_msg = {} 
            result_msg["eid"] = msg["eid"]
            result_msg["dev"] = msg["dev"]
            result_msg["type"] = msg["type"] + "Feedback"
            result_msg["taskNum"] = msg["taskNum"]
            result_msg["sendTime"] = msg["sendTime"]
            result_msg["data"] = {}
            if msg["type"] == "selfCheck":
                print(f"[调试] 处理selfCheck任务")
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["codelist"] = [0]
            elif msg["type"] == "box":
                print(f"[调试] 处理box任务")
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["points"] = []
            elif msg["type"] == "palletCenter":
                print(f"[调试] 处理palletCenter任务")
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["center"] = [0,0,0]
                result_msg["data"]["angle"] = 0
            elif msg["type"] == "placementLocation":
                print(f"[调试] 处理placementLocation任务")
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["points"] = [] #xyzw
                
            elif msg["type"] == "beltBox":
                print(f"[调试] 处理beltBox任务")
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["box"] = "existence"
                result_msg["data"]["point"] = [] #xyzw
            elif msg["type"] == "QRCode":
                print(f"[调试] 处理QRCode任务")
                result_msg["data"]["result"] = "fail"
                result_msg["data"]["code"] = [0]
                result_msg["data"]["coordinates"] = [] #id x y z px py

            else:
                print(f"[调试] 未知任务类型: {msg.get('type', 'None')}")
                print(f"[调试] ====== get_msg失败结束 ======\n")
                return False,None
            print(f"[调试] 生成的result_msg: {result_msg}")
            print(f"[调试] ====== get_msg成功结束 ======\n")
            return True,result_msg
        except Exception as e:
            print(f"[调试] get_msg发生异常: {e}")
            config.log.logger.error("Error getting message: %s", e)
            return False,None

    def result_processing(self, result_list, img):
        try:
            if self.task["type"] == "selfCheck":
                self.msg = {
                    "type": "selfCheckFeedback",
                    "taskNum": self.task.get("taskNum", ""),
                    "data": {"result": "pass", "code": 0, "points": []},
                    "sendTime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                }
                return True, self.msg
            if self.task["type"] == "box":
                if len(result_list) > 0:
                    if isinstance(result_list, list) and len(result_list) > 0:
                        if isinstance(result_list[0], list) and len(result_list[0]) > 0:
                            latest_results = result_list[-1] if len(result_list) > 0 else []
                        else:
                            latest_results = result_list
                    else:
                        latest_results = []
                    # 应用抓取排序算法
                    sorted_boxes = self.sort_boxes_for_grasping(latest_results)
                    grasping_info = self.get_grasping_order_info(sorted_boxes)
                    # 构建order映射（只为最上层分配order）
                    order_map = {}
                    for info in grasping_info:
                        for i, box in enumerate(latest_results):
                            if len(box) >= 3 and (
                                abs(box[0] - info['position'][0]) < 50 and 
                                abs(box[1] - info['position'][1]) < 50 and 
                                abs(box[2] - info['position'][2]) < 50
                            ):
                                order_map[i] = info['order']
                                break
                    # points按抓取顺序排序（order>0的先，order小的在前）
                    points_with_order = []
                    next_points = []
                    for i, box in enumerate(latest_results):
                        x, y, z = box[:3]
                        angle = box[3] if len(box) > 3 else 0
                        d = box[5] if len(box) > 5 else 0  # 直径
                        l = box[4] if len(box) > 4 else 0  # 长度
                        order = order_map.get(i, 0)
                        # 构造数组格式 [x, y, z, angle, order, d, l]
                        entry = [x, y, z, angle, order, d, l]
                        if order > 0:
                            points_with_order.append((order, entry))
                        else:
                            # nextPoints中的order设为0
                            next_entry = [x, y, z, angle, 0, d, l]
                            next_points.append(next_entry)
                    # 按order排序，order小的在前
                    points_with_order.sort(key=lambda x: x[0])
                    points_sorted = [entry for _, entry in points_with_order]

                    # 获取托盘几何信息
                    pallet_geometry = self.get_pallet_geometry_info()

                    # 检查是否有错误直径的布捆
                    wrong_diameter = "none"
                    if hasattr(self, 'defect_info_for_json') and self.defect_info_for_json:
                        error_boxes = []
                        for defect in self.defect_info_for_json:
                            if any("直径" in reason for reason in defect['defect_reasons']):
                                error_boxes.append(f"box{defect['bundle_order']}")
                        if error_boxes:
                            wrong_diameter = ",".join(error_boxes)

                    # 构建严格协议格式，nextPoints只包含order=0的cloth，且无order字段
                    self.msg = {
                        "type": "clothFeedback",
                        "taskNum": self.task.get("taskNum", ""),
                        "data": {
                            "result": "pass",
                            "code": 0,
                            "points": points_sorted,
                            "nextPoints": next_points,
                            "wrong_diameter": wrong_diameter
                        },
                        "sendTime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                    }

                    # 单独发送托盘尺寸信息
                    self.send_pallet_size_info(pallet_geometry)

                    return True, self.msg
                else:
                    # 获取托盘几何信息（即使检测失败也要返回托盘信息）
                    pallet_geometry = self.get_pallet_geometry_info()

                    self.msg = {
                        "type": "clothFeedback",
                        "taskNum": self.task.get("taskNum", ""),
                        "data": {
                            "result": "fail",
                            "code": 0,
                            "points": [],
                            "nextPoints": [],
                            "wrong_diameter": "none"  # 失败时默认没有错误直径信息
                        },
                        "sendTime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
                    }

                    # 单独发送托盘尺寸信息
                    self.send_pallet_size_info(pallet_geometry)

                    return True, self.msg
            elif self.task["type"] == "palletCenter":
                ret = True                
                result = self.pallet_center
            elif self.task["type"] == "placementLocation":
                print(f"[result_processing调试] 处理placementLocation结果")
                print(f"[result_processing调试] result_list类型: {type(result_list)}")
                print(f"[result_processing调试] result_list长度: {len(result_list) if result_list else 0}")

                if result_list and len(result_list) > 0:
                    # result_list是多次检测结果的列表，每个元素是一次检测的sorted_boxes
                    # 我们使用最新的检测结果（最后一个元素）
                    latest_result = result_list[-1]  # 获取最新的检测结果
                    print(f"[result_processing调试] 使用最新检测结果，类型: {type(latest_result)}")

                    if isinstance(latest_result, list) and len(latest_result) > 0:
                        # 现在只有一个最终的放置位置
                        final_box = latest_result[0]
                        print(f"[result_processing调试] 处理最终放置位置: {final_box}")

                        if isinstance(final_box, dict):
                            # 可视化相关参数
                            font = cv2.FONT_HERSHEY_SIMPLEX
                            font_scale = 1
                            color = (0, 246, 26)
                            thickness = 2

                            # 绘制矩形框（绿色表示可放置）
                            cv2.rectangle(img, (final_box['tl_pt'][0], final_box['tl_pt'][1]),
                                        (final_box['br_pt'][0], final_box['br_pt'][1]), (0, 255, 0), 3)

                            # 添加坐标信息（左侧）
                            text_x = int(final_box['tl_pt'][0]) + 10
                            text_y = int(final_box['tl_pt'][1]) + 30

                            # 绘制序号（红色）
                            cv2.putText(img, f"#1", (text_x, text_y),
                                      font, 1.5, (0, 0, 255), 3)
                            # 绘制坐标信息（绿色）
                            cv2.putText(img, f"x:{final_box['x']}", (text_x, text_y + 40),
                                      font, font_scale, color, thickness)
                            cv2.putText(img, f"y:{final_box['y']}", (text_x, text_y + 70),
                                      font, font_scale, color, thickness)
                            cv2.putText(img, f"z:{final_box['z']}", (text_x, text_y + 100),
                                      font, font_scale, color, thickness)

                            # 创建返回的点列表（只有一个点）
                            pt = [final_box["x"], final_box["y"], final_box["z"], 1]
                            pts = [pt]

                            # 设置成功结果
                            self.msg["data"]["result"] = "pass"
                            self.msg["data"]["code"] = [0]
                            self.msg["data"]["points"] = pts
                            self.msg["data"]["minH"] = int(final_box["z"])
                            self.msg["data"]["codelist"] = [1]

                            print(f"[result_processing调试] 成功处理最终放置位置: x={final_box['x']}, y={final_box['y']}, z={final_box['z']}")
                        else:
                            self.msg["data"]["result"] = "fail"
                            self.msg["data"]["code"] = [0]
                            self.msg["data"]["points"] = []
                            print(f"[result_processing调试] final_box不是字典")
                    else:
                        self.msg["data"]["result"] = "fail"
                        self.msg["data"]["code"] = [0]
                        self.msg["data"]["points"] = []
                        print(f"[result_processing调试] latest_result为空或无效")

                    # 显示结果
                    self.resize_img(img, 'placementLocation-' + self.id)
                    return True, self.msg
                else:
                    self.msg["data"]["result"] = "fail"
                    self.msg["data"]["code"] = [0]
                    self.msg["data"]["points"] = []
                    print(f"[result_processing调试] result_list为空或None")
                    return True, self.msg

            elif self.task["type"] == "QRCode":
                self.msg["data"]["result"] = "pass"
                self.msg["data"]["code"] = [0]
                ids = [result[0] for result in result_list]
                if len(set(ids)) != 1:
                    return False,None
                get_id = ids[0]
                # 使用 zip 和列表推导式求和
                sum_values = [sum(values) for values in zip(*result_list)][1:]  # 跳过第一个元素，因为它是 id
                # 计算平均值
                average_values = [int(val / len(result_list)) for val in sum_values]
                #sum_values = sum(result[1:] for result in result_list)  # 求和
                #average_values = [int(val / len(result_list)) for val in sum_values]
                self.msg["data"]["coordinates"] = [get_id] + average_values
                px = self.msg["data"]["coordinates"][4]
                py = self.msg["data"]["coordinates"][5]
                cv2.circle(img, (px, py), 3, (0, 255, 0), -1)
                cv2.putText(img, f"id:{get_id} x:{average_values[0]} y:{average_values[1]} z:{average_values[2]}", (px - 180, py + 40), cv2.FONT_HERSHEY_SIMPLEX, 1, (126, 0, 0), 2)
                self.resize_img(img, 'QRCode-' + self.id)
                return True, self.msg
                
            else:
                return False,None
            return True,self.msg
        except Exception as e:
            config.log.logger.error("Error result_processing: %s", e)
            return False, None

    def get_img_result(self, np_depth, mat_color):
        ret = False
        result = None
        try:
            if self.task["type"] == "QRCode":
                ret, result = self.task_qrcode(np_depth,mat_color)
            elif self.task["type"] == "box":
                print(f"\n[相机模式调试] ====== 开始处理box任务 ======")
                print(f"[相机模式调试] 当前result_list长度: {len(getattr(self, 'result_list', []))}")
                
                # 获取抓取顺序映射
                grasping_order_map = None
                if hasattr(self, 'msg') and self.msg and 'data' in self.msg and 'grasping_order_map' in self.msg['data']:
                    grasping_order_map = self.msg['data']['grasping_order_map']
                    print(f"[相机模式调试] 从msg中获取到grasping_order_map: {grasping_order_map}")
                else:
                    print(f"[相机模式调试] msg中没有grasping_order_map，将在task_box_process中生成")
                
                print(f"[相机模式调试] 最终传递给task_box的grasping_order_map: {grasping_order_map}")
                ret, result, mat_color = self.task_box(np_depth, mat_color, grasping_order_map)

                # 处理缺陷信息
                if hasattr(self, 'defect_list') and self.defect_list:
                    defect_info_list = []
                    for defect in self.defect_list:
                        if defect['has_defect']:
                            defect_info = {
                                'bundle_order': defect['grasping_order'],
                                'defect_reasons': defect['defect_reasons'],
                                'defect_summary': ', '.join(defect['defect_reasons'])
                            }
                            defect_info_list.append(defect_info)
                    self.defect_info_for_json = defect_info_list
                    print(f"[相机模式调试] 处理缺陷信息完成，共{len(defect_info_list)}个异常布捆")
                else:
                    self.defect_info_for_json = []
                    print(f"[相机模式调试] 无缺陷信息")

                if ret and mat_color is not None:
                    print(f"[相机模式调试] task_box执行成功，检测到{len(result) if result else 0}个箱子")
                    self.resize_img(mat_color, 'box-' + self.id,scale_factor = 0.4)
                else:
                    print(f"[相机模式调试] task_box执行失败或未检测到箱子")
                    
            elif self.task["type"] == "palletCenter":
                ret = True                
                result = self.pallet_center
            elif self.task["type"] == "placementLocation":
                ret, result = self.task_placementLocation(np_depth,mat_color)
            return ret,result
        except Exception as e:
            config.log.logger.error("Error processing images: %s", e)
            print(f"[相机模式调试] get_img_result发生异常: {e}")
            return False, None

    def task_qrcode(self, np_depth, mat_color): #二维码检测
        try:
            for i in range(4):
                alp = 0.5 + 0.3 * i
                imgA = cv2.convertScaleAbs(mat_color, alpha=alp)
                imgA = cv2.GaussianBlur(imgA, (3, 3), 0)
                grayA = cv2.cvtColor(imgA, cv2.COLOR_BGR2GRAY)
                if QRCODE == "CV2":
                    corners, ids, rejectedImgPoints = cv2.aruco.detectMarkers(grayA, self.detector)
                    if ids is not None:
                        for i, (corner, id) in enumerate(zip(corners, ids)):
                            # 计算中心坐标
                            center_x = int(sum(corner[0][:, 0]) / len(corner[0]))
                            center_y = int(sum(corner[0][:, 1]) / len(corner[0]))
                            (px, py) = (int(center_x), int(center_y))
                            # 输出ID和中心坐标
                            print(f"Tag {id[0]} center at: ({center_x}, {center_y})")
                            tag_id = id[0]
                            ret, result = self.get_code_depth(np_depth, px, py, 25, tag_id)
                            print("result",result)
                            if ret:
                                return True,result
                else:
                    results = self.detector.detect(grayA)
                    if results is not None:
                        for r in results:
                            pt = self.get_code_points(r)
                            (px, py) = (int(r.center[0]), int(r.center[1]))
                            tag_id = r.tag_id
                            ret, result = self.get_code_depth(np_depth, px, py, pt[2], tag_id)
                            print("result",result)
                            if ret:
                                return True,result
            return False, None
        except Exception as e:
            config.log.logger.error("Error task_qrcode: %s", e)
            return False, None
    def task_placementLocation(self, np_depth, mat_color):
        try:
            print(f"\n[放置位置调试] ====== 开始双重检测逻辑 ======")

            # 初始化相机参数
            self.pinhole_camera_intrinsic = o3d.camera.PinholeCameraIntrinsic(
                self.img_w, self.img_h, self.depth_calib_intr[0], self.depth_calib_intr[4],
                self.depth_calib_intr[2], self.depth_calib_intr[5])

            # 获取托盘和箱子参数
            pallet_l = self.pallet_center["l"]  # 长
            pallet_w = self.pallet_center["w"]  # 宽
            pallet_h = self.pallet_center["h"]  # 高
            pallet_center_x = self.pallet_center["x"]  # x
            pallet_center_y = self.pallet_center["y"]  # y

            box_l = self.task["task"]["length"]
            box_h = self.task["task"]["height"]

            # 第一次检测：width=250，显示4个检测框
            print(f"[放置位置调试] 第一次检测：width=250")
            box_w_1 = 250
            detection_1 = self.perform_placement_detection(np_depth, mat_color, pallet_l, pallet_w, pallet_h,
                                                         pallet_center_x, pallet_center_y, box_l, box_w_1, box_h, "4框模式")

            # 第二次检测：width=225，显示5个检测框
            print(f"[放置位置调试] 第二次检测：width=225")
            box_w_2 = 225
            detection_2 = self.perform_placement_detection(np_depth, mat_color, pallet_l, pallet_w, pallet_h,
                                                         pallet_center_x, pallet_center_y, box_l, box_w_2, box_h, "5框模式")

            if not detection_1 or not detection_2:
                print("[放置位置调试] 检测失败，无法获取有效数据")
                return False, None

            # 比较两次检测的最高z值
            max_z_1 = max(element['z'] for element in detection_1)
            max_z_2 = max(element['z'] for element in detection_2)

            print(f"[放置位置调试] 4框模式最高z值: {max_z_1}")
            print(f"[放置位置调试] 5框模式最高z值: {max_z_2}")

            # 选择z值更高的模式
            if max_z_1 >= max_z_2:
                selected_mode = "4框模式"
                selected_detection = detection_1
                selected_max_z = max_z_1
                selected_width = box_w_1
            else:
                selected_mode = "5框模式"
                selected_detection = detection_2
                selected_max_z = max_z_2
                selected_width = box_w_2

            print(f"[放置位置调试] 选择模式: {selected_mode}，最高z值: {selected_max_z}")

            # 找到z值最高的目标检测框
            target_element = None
            target_index = -1
            for i, element in enumerate(selected_detection):
                if element['z'] == selected_max_z:
                    target_element = element
                    target_index = i
                    break

            if target_element is None:
                print("[放置位置调试] 未找到目标检测框")
                return False, None

            print(f"[放置位置调试] 目标检测框位置: x={target_element['x']}, y={target_element['y']}, z={target_element['z']}")

            # 比较目标检测框与两侧的z坐标，确定最终放置高度
            final_placement_z = self.calculate_final_placement_height(selected_detection, target_index, target_element)

            print(f"[放置位置调试] 最终放置高度: {final_placement_z}")

            # 更新目标元素的放置高度
            target_element['placement_height'] = final_placement_z
            target_element['selected_mode'] = selected_mode
            target_element['width'] = selected_width

            return True, [target_element]

        except Exception as e:
            print(f"[放置位置调试] task_placementLocation发生错误: {str(e)}")
            traceback.print_exc()
            return False, None

    def perform_placement_detection(self, np_depth, mat_color, pallet_l, pallet_w, pallet_h,
                                  pallet_center_x, pallet_center_y, box_l, box_w, box_h, mode_name):
        """执行单次放置位置检测"""
        try:
            # 计算可放置的箱子数量
            num_boxes_along_length = pallet_l // box_l
            num_boxes_along_width = pallet_w // box_w
            box_l_gap = int(pallet_l / num_boxes_along_length)
            box_w_gap = int(pallet_w / num_boxes_along_width)

            # 生成放置位置坐标
            box_coordinates = []
            for i in range(num_boxes_along_width):
                y = pallet_center_y - pallet_w/2 + box_w_gap/2 + i*box_w_gap
                for j in range(num_boxes_along_length):
                    x = pallet_center_x - pallet_l/2 + box_l_gap/2 + j*box_l_gap
                    box_coordinates.append([x, y])

            print(f"[放置位置调试] {mode_name}: 生成了{len(box_coordinates)}个可能的放置位置")

            # 初始化location_grid
            location_grid = []
            for x, y in box_coordinates:
                # 获取点云
                obb = self.get_box_point_cloud(np_depth, x, y, box_l, box_w)
                if obb is not None:
                    element = {
                        'x': x,
                        'y': y,
                        'z': int(obb.min_bound[2] * 1000),  # 从点云中获取该区域的最低点高度
                        'placement_height': int(obb.min_bound[2] * 1000),  # 初始化放置高度
                    }
                    # 获取绘制矩形的坐标
                    element['tl_pt'], element['br_pt'] = self.get_box_draw_rect(x, y, element['z'], box_l, box_w)
                    location_grid.append(element)

            if not location_grid:
                print(f"[放置位置调试] {mode_name}: 没有找到有效的放置位置")
                return None

            print(f"[放置位置调试] {mode_name}: 找到{len(location_grid)}个有效位置")
            return location_grid

        except Exception as e:
            print(f"[放置位置调试] {mode_name}检测发生错误: {str(e)}")
            return None

    def calculate_final_placement_height(self, detection_grid, target_index, target_element):
        """计算最终放置高度：比较目标框与两侧框的z坐标，取最小值"""
        try:
            target_z = target_element['z']
            left_z = None
            right_z = None

            # 获取左侧检测框的z值
            if target_index > 0:
                left_z = detection_grid[target_index - 1]['z']
                print(f"[放置位置调试] 左侧检测框z值: {left_z}")
            else:
                print(f"[放置位置调试] 目标框位于最左侧，无左侧检测框")

            # 获取右侧检测框的z值
            if target_index < len(detection_grid) - 1:
                right_z = detection_grid[target_index + 1]['z']
                print(f"[放置位置调试] 右侧检测框z值: {right_z}")
            else:
                print(f"[放置位置调试] 目标框位于最右侧，无右侧检测框")

            # 收集所有有效的z值
            z_values = [target_z]
            if left_z is not None:
                z_values.append(left_z)
            if right_z is not None:
                z_values.append(right_z)

            # 取最小的z坐标作为最终放置高度
            final_z = min(z_values)

            print(f"[放置位置调试] 目标框z值: {target_z}")
            print(f"[放置位置调试] 比较的z值: {z_values}")
            print(f"[放置位置调试] 选择的最小z值: {final_z}")

            return final_z

        except Exception as e:
            print(f"[放置位置调试] 计算最终放置高度发生错误: {str(e)}")
            return target_element['z']  # 出错时返回目标框自身的z值

    def get_box_draw_rect(self, x, y, z, l, w):
        tl_pt = self.xyz2depth(x - l / 2, y - w / 2, z)
        br_pt = self.xyz2depth(x + l / 2, y + w / 2, z)
        return tl_pt, br_pt

    def draw_rotated_rect(self, img, x, y, z, l, w, angle_degrees, color=(0, 0, 255), thickness=2):
        """
        绘制旋转的矩形
        参数:
            img: 要绘制的图像
            x, y, z: 矩形中心的3D坐标
            l, w: 矩形的长宽
            angle_degrees: 旋转角度（度）
            color: 颜色
            thickness: 线条粗细
        """
        import math

        # 将角度转换为弧度
        angle_rad = math.radians(angle_degrees)

        # 计算矩形四个角点的相对坐标（相对于中心点）
        half_l = l / 2
        half_w = w / 2

        # 原始四个角点（未旋转）
        corners_3d = [
            [-half_l, -half_w],  # 左上
            [half_l, -half_w],   # 右上
            [half_l, half_w],    # 右下
            [-half_l, half_w]    # 左下
        ]

        # 应用旋转变换
        rotated_corners_3d = []
        for corner in corners_3d:
            # 旋转公式：x' = x*cos(θ) - y*sin(θ), y' = x*sin(θ) + y*cos(θ)
            rotated_x = corner[0] * math.cos(angle_rad) - corner[1] * math.sin(angle_rad)
            rotated_y = corner[0] * math.sin(angle_rad) + corner[1] * math.cos(angle_rad)
            rotated_corners_3d.append([x + rotated_x, y + rotated_y, z])

        # 将3D坐标转换为2D像素坐标
        corners_2d = []
        for corner in rotated_corners_3d:
            px, py = self.xyz2depth(corner[0], corner[1], corner[2])
            corners_2d.append((int(px), int(py)))

        # 绘制矩形（连接四个角点）
        import numpy as np
        pts = np.array(corners_2d, np.int32)
        pts = pts.reshape((-1, 1, 2))
        cv2.polylines(img, [pts], True, color, thickness)

    def get_pallet_geometry_info(self):
        """
        获取基于旋转后显示框的托盘几何信息，包括中心点坐标、长宽高、距离上下边的距离
        返回:
            dict: 包含托盘几何信息的字典
        """
        try:
            import math

            # 旋转角度（与显示框一致）
            rotation_angle = -1.5  # 逆时针旋转1.5度
            angle_rad = math.radians(rotation_angle)

            # 原始托盘中心点坐标（这个保持不变，因为旋转是围绕中心点进行的）
            center_x = self.pallet_center["x"]
            center_y = self.pallet_center["y"]
            center_z = self.pallet_center["z"]

            # 原始托盘尺寸
            original_length = self.pallet_center["l"]  # 长
            original_width = self.pallet_center["w"]   # 宽
            pallet_height = self.pallet_center["h"]    # 高（高度不受旋转影响）

            # 计算旋转后的四个角点坐标
            half_l = original_length / 2
            half_w = original_width / 2

            # 原始四个角点（相对于中心点）
            corners = [
                [-half_l, -half_w],  # 左上
                [half_l, -half_w],   # 右上
                [half_l, half_w],    # 右下
                [-half_l, half_w]    # 左下
            ]

            # 应用旋转变换得到旋转后的角点
            rotated_corners = []
            for corner in corners:
                # 旋转公式：x' = x*cos(θ) - y*sin(θ), y' = x*sin(θ) + y*cos(θ)
                rotated_x = corner[0] * math.cos(angle_rad) - corner[1] * math.sin(angle_rad)
                rotated_y = corner[0] * math.sin(angle_rad) + corner[1] * math.cos(angle_rad)
                rotated_corners.append([center_x + rotated_x, center_y + rotated_y])

            # 计算旋转后的边界框尺寸
            x_coords = [corner[0] for corner in rotated_corners]
            y_coords = [corner[1] for corner in rotated_corners]

            min_x, max_x = min(x_coords), max(x_coords)
            min_y, max_y = min(y_coords), max(y_coords)

            # 旋转后的实际长宽（边界框尺寸）
            rotated_length = max_x - min_x
            rotated_width = max_y - min_y

            # 计算距离上下边的距离（基于旋转后的框）
            distance_to_top = (max_y - center_y)      # 中心点到上边的距离
            distance_to_bottom = (center_y - min_y)   # 中心点到下边的距离

            # 构造返回的几何信息（基于旋转后的实际显示框）
            geometry_info = {
                "center": {
                    "x": center_x,
                    "y": center_y,
                    "z": center_z
                },
                "dimensions": {
                    "length": round(rotated_length, 2),    # 旋转后的长度
                    "width": round(rotated_width, 2),      # 旋转后的宽度
                    "height": pallet_height                # 高度不变
                },
                "distances": {
                    "to_top_edge": round(distance_to_top, 2),     # 距离上边的距离
                    "to_bottom_edge": round(distance_to_bottom, 2) # 距离下边的距离
                },
                "rotation_angle": rotation_angle,  # 旋转角度
                "original_dimensions": {           # 原始尺寸（供参考）
                    "length": original_length,
                    "width": original_width
                },
                "rotated_corners": [              # 旋转后的四个角点坐标
                    {"x": round(corner[0], 2), "y": round(corner[1], 2)} for corner in rotated_corners
                ]
            }

            print(f"[托盘几何信息] 中心点: ({center_x}, {center_y}, {center_z})")
            print(f"[托盘几何信息] 原始尺寸: 长={original_length}, 宽={original_width}")
            print(f"[托盘几何信息] 旋转后尺寸: 长={rotated_length:.2f}, 宽={rotated_width:.2f}")
            print(f"[托盘几何信息] 距离边缘: 上={distance_to_top:.2f}, 下={distance_to_bottom:.2f}")
            print(f"[托盘几何信息] 旋转角度: {rotation_angle}度")

            return geometry_info

        except Exception as e:
            print(f"[托盘几何信息] 获取失败: {e}")
            return None

    def send_pallet_size_info(self, pallet_geometry):
        """
        单独发送托盘尺寸信息
        参数:
            pallet_geometry: 托盘几何信息字典
        """
        try:
            if pallet_geometry is None:
                print(f"[托盘尺寸上报] 托盘几何信息为空，跳过上报")
                return

            # 构造托盘尺寸上报消息，按照协议格式
            pallet_size_msg = {
                "type": "palletSizeFeedback",
                "taskNum": self.task.get("taskNum", "") + "_size",
                "data": {
                    "result": "pass",
                    "code": 0,
                    "size": {
                        "center": [
                            pallet_geometry["center"]["x"],
                            pallet_geometry["center"]["y"],
                            pallet_geometry["center"]["z"]
                        ],
                        "length": pallet_geometry["dimensions"]["length"],
                        "width": pallet_geometry["dimensions"]["width"],
                        "distance_to_top": pallet_geometry["distances"]["to_top_edge"],
                        "distance_to_bottom": pallet_geometry["distances"]["to_bottom_edge"]
                    }
                },
                "sendTime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
            }

            print(f"[托盘尺寸上报] 发送托盘尺寸信息: {pallet_size_msg}")

            # 这里可以添加实际的发送逻辑，比如通过socket或其他通信方式发送
            # 目前先打印到控制台
            import json
            print(f"[托盘尺寸上报] JSON: {json.dumps(pallet_size_msg, ensure_ascii=False, indent=4)}")

        except Exception as e:
            print(f"[托盘尺寸上报] 发送失败: {e}")


    def task_box(self, np_depth, mat_color, grasping_order_map=None): #检测
        try:
            box_results = []
            output, results = self.task_box_process(mat_color, np_depth, config.box_model, grasping_order_map)
            if results is not None:
                for result in results:
                    x, y, z, a, px, py, w, l = result
                    # 直径w和长度l直接用YOLO返回的w和l
                    box_results.append([x, y, z, a, l, w])
                return True, box_results, output
            else:
                return False, None, None
        except Exception as e:
            config.log.logger.error("Error task_box: %s", e)
            return False, None, None
    
    def get_box_wl(self, box):
        x,y,z,a,px,py,w,l = box
        pl = self.depth2xyz(-l/2, 0, z)
        pr = self.depth2xyz(l/2, 0, z)
        l_mm = int(self.pt_distance(pl, pr))
        w_mm = int(l_mm * w / l)
        return w_mm, l_mm
    
    def task_box_process(self, img, depthimg, model, grasping_order_map=None):
        try:
            print(f"\n[相机模式调试] ====== task_box_process开始 ======")
            print(f"[相机模式调试] 接收到的grasping_order_map: {grasping_order_map}")
            print(f"[相机模式调试] 图像尺寸: {img.shape if img is not None else 'None'}")
            print(f"[相机模式调试] 深度图尺寸: {depthimg.shape if depthimg is not None else 'None'}")
            
            starTime = time.time()
            # 推理
            boxes, segments, _ = model(img, conf_threshold=0.35, iou_threshold=0.45)
            
            # 正确处理boxes类型：可能是NumPy数组、列表或元组
            if boxes is None:
                print(f"警告：boxes为None，强制置空")
                boxes = []
            elif hasattr(boxes, '__len__') and len(boxes) > 0:
                # 如果是NumPy数组或其他可迭代对象，转换为列表
                if hasattr(boxes, 'tolist'):
                    boxes = boxes.tolist()
                elif not isinstance(boxes, (list, tuple)):
                    boxes = list(boxes)
                print(f"[相机模式调试] boxes类型: {type(boxes)}, 长度: {len(boxes)}")
            else:
                print(f"警告：boxes为空或无效，强制置空: {boxes}")
                boxes = []
                
            print(f"[相机模式调试] 模型推理完成，检测到{len(boxes)}个目标")
            
            # 画图
            output_image, result = None, None
            if len(boxes) > 0:
                # 如果没有grasping_order_map，尝试生成一个
                if grasping_order_map is None:
                    print(f"[相机模式调试] 没有grasping_order_map，尝试生成一个")
                    temp_draw_result = model.draw_and_visualize(img, boxes, segments, depthimg, self.depth_calib_intr, vis=False, save=False, xyz_range=self.xyz_range)
                    if len(temp_draw_result) == 3:
                        temp_output, temp_result, _ = temp_draw_result  # 忽略缺陷信息
                    else:
                        temp_output, temp_result = temp_draw_result
                    if not isinstance(temp_result, (list, tuple)):
                        print(f"警告：temp_result类型异常，强制置空: {temp_result}")
                        temp_result = []
                    if len(temp_result) > 0:
                        boxes_for_sorting = []
                        for box_result in temp_result:
                            if not isinstance(box_result, (list, tuple)):
                                print(f"警告：box_result类型异常，跳过：{box_result}")
                                continue
                            x, y, z, angle, px, py, short_edge, long_edge = box_result
                            boxes_for_sorting.append([x, y, z, angle, long_edge, short_edge])
                        sorted_boxes = self.sort_boxes_for_grasping(boxes_for_sorting)
                        grasping_info = self.get_grasping_order_info(sorted_boxes)
                        # 只为最上层目标分配抓取顺序编号
                        grasping_order_map = {}
                        for info in grasping_info:
                            for i, original_box in enumerate(temp_result):
                                if not isinstance(original_box, (list, tuple)):
                                    continue
                                if (abs(original_box[0] - info['position'][0]) < 50 and 
                                    abs(original_box[1] - info['position'][1]) < 50 and 
                                    abs(original_box[2] - info['position'][2]) < 50):
                                    grasping_order_map[i] = info['order']
                                    print(f"[相机模式调试] 匹配最上层箱子{i}: 位置({original_box[0]},{original_box[1]},{original_box[2]}) -> 抓取顺序#{info['order']}")
                                    break
                        print(f"[相机模式调试] 只为最上层目标分配抓取顺序编号: {grasping_order_map}")
                    else:
                        print(f"[相机模式调试] 没有检测到箱子，无法生成grasping_order_map")
                        grasping_order_map = {}
                print(f"[相机模式调试] 开始调用draw_and_visualize，传递grasping_order_map: {grasping_order_map}")
                draw_result = model.draw_and_visualize(img, boxes, segments, depthimg, self.depth_calib_intr, vis=False, save=True, xyz_range=self.xyz_range, grasping_order_map=grasping_order_map)

                # 处理返回值：可能是2个值或3个值
                if len(draw_result) == 3:
                    output_image, result, defect_list = draw_result
                    # 存储缺陷信息供后续使用
                    self.defect_list = defect_list
                    print(f"[相机模式调试] 获取到缺陷检测信息: {defect_list}")
                else:
                    output_image, result = draw_result
                    self.defect_list = []
                    print(f"[相机模式调试] 未获取到缺陷检测信息，使用空列表")

                if not isinstance(result, (list, tuple)):
                    print(f"警告：draw_and_visualize返回result类型异常，强制置空: {result}")
                    result = []
                print(f"[相机模式调试] draw_and_visualize完成，返回{len(result) if result else 0}个结果")
                if result:
                    for i, res in enumerate(result):
                        if not isinstance(res, (list, tuple)):
                            print(f"警告：res类型异常，跳过：{res}")
                            continue
                        print(f"[相机模式调试] 结果{i}: x={res[0]}, y={res[1]}, z={res[2]}, angle={res[3]}, px={res[4]}, py={res[5]}, diameter={res[6]}, length={res[7]}")
            else:
                output_image = img
                result = None
                print(f"[相机模式调试] 未检测到目标，使用原图")
                
            print("图片完成检测")
            useTime = time.time() - starTime
            useTime = round(useTime, 2)
            textTime = f"useTime: {useTime} seconds - {self.image_id}"
            cv2.putText(output_image, textTime, (int(10), int(30)), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

            # 在box任务的输出图像上绘制托盘边框（逆时针旋转1.5度）
            try:
                self.draw_rotated_rect(output_image, self.pallet_center["x"], self.pallet_center["y"], self.pallet_center["z"],
                                      self.pallet_center["l"], self.pallet_center["w"], -1.5, (0, 0, 255), 2)  # 逆时针旋转1.5度
                print(f"[相机模式调试] 已在box任务图像上绘制旋转托盘边框（逆时针1.5度）")
            except Exception as e:
                print(f"[相机模式调试] 绘制旋转托盘边框失败: {e}")

            print(f"[相机模式调试] task_box_process完成，耗时{useTime}秒")
            # cv2.imshow("box"+self.id, output_image)
            # cv2.waitKey(1)
            return output_image, result

        except Exception as e:            
            config.log.logger.error("Error task_box_process: %s", e)
            print(f"[相机模式调试] task_box_process发生异常: {e}")
            return None, None

    def run_camera_in_main_thread(self):
        print('run_camera_in_main_thread:')
        if self.use_local_image and self.local_image_folder:
           
            while True:
                image_list = self.get_images()
                if image_list:
                   
                    self.process_images(image_list)
                else:
                 
                    break  # 本地图片处理完毕后退出
            return
        if not self.start_stream():
            config.log.logger.error(f"Failed to start stream for camera")
            return
        try:
            while True:
                time.sleep(0.01)
                status = self.cl.DeviceGetParameter(self.handle, TY_COMPONENT_RGB_CAM, TY_BOOL_AUTO_EXPOSURE)
                m_status = status.toBool()
                image_list = self.get_images()
                if image_list:
                    print(f'[调试] run_camera_in_main_thread循环, self.start={self.start}')
                    print('get_images 返回图片，调用 process_images')
                    self.process_images(image_list)
                else:
                    print('get_images 返回 None，跳过本次')
        finally:
            self.stop_stream()
            self.close_camera()

    def get_code_depth(self, depthImage,  cX, cY, minDistance, id):
        """
        根据深度图像和编码信息获取编码的深度信息。
        :param depthImage: 深度图像，用于获取编码区域的深度信息
        :param code: 编码信息，包含编码的位置和尺寸等信息
        :return: 返回一个元组，包含编码
        """
        try:
            distancesList = []
            # (cX, cY) = (int(code.center[0]), int(code.center[1]))
            # distances = [self.ptDistance((cX, cY), corner) for corner in code.corners]
            # minDistance = min(distances)
            randomPoints = self.generate_random_points(cX, cY, minDistance, 50)
            for (px,py) in randomPoints:
                dist = depthImage[py,px]
                if dist != 0 and dist < 4000 and dist > 350:
                    distancesList.append(dist)
            if len(distancesList) > 0:
                meanDistance = int(np.mean(distancesList))
                ptDepth = self.depth2xyz(cX, cY, meanDistance)
                return True, [id, int(ptDepth[0]), int(ptDepth[1]), int(ptDepth[2]), cX, cY]
        except:
            return False, None

    def get_code_points(self, code):
        """
        计算给定二维码的中心坐标和其到四个角的最短距离。
        :param code: 二维码对象，包含二维码的中心和四个角的信息。
        :return: 返回一个元组，包含二维码中心的X坐标、Y坐标以及中心到四个角的最短距离（单位为像素）。
        """
        (cX, cY) = (int(code.center[0]), int(code.center[1]))
        distances = [self.pt_distance((cX, cY), corner) for corner in code.corners]
        minDistance = min(distances)
        return cX, cY, int(minDistance)

    def generate_random_points(self, x, y, R, n):
        """
        生成在以(x, y)为圆心、半径为R的圆内的随机点。
        param:
        x, y: 圆心的坐标
        R: 圆的半径
        n: 需要生成的点的数量
        return:
        一个包含n个在圆内的随机点的列表，每个点都是一个(x, y)坐标元组。
        """
        points = []
        while len(points) < n:
            theta = random.uniform(0, 2 * np.pi)
            r = random.uniform(0, R)
            xp = x + r * np.cos(theta)
            yp = y + r * np.sin(theta)
            if 0 <= xp < self.img_w and 0 <= yp < self.img_h:
                points.append((int(xp), int(yp)))
        return points

    def pt_distance(self, p1, p2):
        return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

    def save_csv(self, img, datas, image_id, start_time,img_ori = None,img_depth = None):
        save_data = {}
        #识别开始时间
        #imageId = 1
        #识别结束时间
        end_time = time.time()
        #识别使用时间
        useT_time = end_time - start_time
        #文件名
        save_time = time.strftime('_%Y_%m_%d_%H_%M_%S')
        datas["useT_time"] = useT_time
        datas["save_time"] = save_time
        datas["image_id"] = image_id
        # 将'task'中的键提到上一级
        if 'task' in datas:
            for key, value in datas['task'].items():
                datas[key] = value  # 将'task'中的键值对添加到data中
            del datas['task']
        #dates.update(dict2)
        save_name = str(datas.get("dev", "unknown")) + "-" + str(datas.get("type", "unknown")) + save_time + "-" + str(image_id) 
        try:
            # 设置目录路径
            directory = "./data"
            # 获取当前的年月日
            today = datetime.datetime.now().strftime("%Y%m%d")
            # 构建完整的文件夹路径
            folderPath = os.path.join(directory, today)
            # 检查文件夹是否存在
            if not os.path.exists(folderPath):
                # 如果文件夹不存在，则创建它
                os.makedirs(folderPath)
            save_path = os.path.join(folderPath, save_name + ".jpg")
            print("save_path: ", save_path)
            cv2.imwrite(save_path, img)
            if img_ori is not None:
                save_path = os.path.join(folderPath, save_name + "_ori" + ".jpg")
                cv2.imwrite(save_path, img_ori)
            if img_depth is not None:
                save_path = os.path.join(folderPath, save_name + "_depth" + ".png")
                cv2.imwrite(save_path, img_depth)
            WriteDataFile = os.path.join(folderPath, 'Data.csv')
            with open(WriteDataFile, "a+", newline='') as f:
                # with open(birth_weight_file, "w") as f:
                writer = csv.writer(f)
                writer.writerow(datas.values())
                f.close()
        except Exception as e:
            print("save_csv error: {}".format(e))

    def sort_boxes_for_grasping(self, boxes):
        """
        只抓取最上层箱子。分层逻辑：z距离最小的布捆的直径的一半，上下波动一半直径的区间认为是同一层。
        排序优先级：1.最上层优先（z最小±0.5*直径）2.同层内离中心(y=0)越近越优先 3.同距离时直径大的优先。
        """
        if not boxes:
            print("[抓取排序调试] 没有检测到箱子，返回空列表")
            return []
        # 处理result_list的结构：直接用全部boxes，不再只取最后一个
        if isinstance(boxes, list) and len(boxes) > 0:
            if isinstance(boxes[0], list) and len(boxes[0]) > 0 and all(isinstance(b, (list, tuple)) for b in boxes):
                actual_boxes = boxes
                print(f"[抓取排序调试] 检测到二维数组结构，使用全部，长度: {len(actual_boxes)}")
            else:
                actual_boxes = boxes
                print(f"[抓取排序调试] 检测到一维数组结构，长度: {len(actual_boxes)}")
        else:
            actual_boxes = []
            print(f"[抓取排序调试] boxes结构异常，置空")
        print(f"[抓取排序调试] 处理后的actual_boxes: {actual_boxes}")
        if not actual_boxes:
            print("[抓取排序调试] actual_boxes为空，返回空列表")
            return []
        if not isinstance(actual_boxes[0], (list, tuple)):
            print(f"[抓取排序调试] actual_boxes是单个箱子数据，包装成列表")
            actual_boxes = [actual_boxes]
        # 计算直径和面积
        boxes_with_diameter = []
        for i, box in enumerate(actual_boxes):
            if isinstance(box, (list, tuple)) and len(box) >= 6:
                x, y, z, angle, length, width = box[:6]
                box_diameter = width
                box_area = length * width
                print(f"[直径调试] box{i}: length={length}, width={width}, 直径={box_diameter}, 面积={box_area}")
                boxes_with_diameter.append(list(box) + [box_diameter, box_area])
                print(f"[抓取排序调试] box{i}: z={z}, y={y}, 直径={box_diameter}")
            else:
                print(f"警告：箱子{i}数据不完整或格式错误，跳过: {box}")
        if not boxes_with_diameter:
            print("[抓取排序调试] 没有有效的箱子数据，返回空列表")
            return []
        # 按z从小到大排序（z小=离相机近=最上层）
        boxes_sorted_by_z = sorted(boxes_with_diameter, key=lambda x: x[2])
        z_min = boxes_sorted_by_z[0][2]
        diameter = boxes_sorted_by_z[0][6]
        z_threshold = diameter * 0.45
        print(f"[抓取排序调试] 最上层z_min: {z_min}, 直径: {diameter}, z阈值: ±{z_threshold}")
        # 只保留z在z_min±0.5*直径区间的箱子
        top_layer_boxes = []
        for i, box in enumerate(boxes_sorted_by_z):
            z = box[2]
            if abs(z - z_min) <= z_threshold:
                top_layer_boxes.append(box)
                print(f"[抓取排序调试] 归为最上层: box{i} z={z}, y={box[1]}")
            else:
                print(f"[抓取排序调试] 跳过下层: box{i} z={z}, y={box[1]}")
        print(f"[抓取排序调试] 最上层箱子数量: {len(top_layer_boxes)}")
        if not top_layer_boxes:
            print("[抓取排序调试] 未找到有效的最上层箱子，返回空列表")
            return []
        # 对最上层的箱子排序：先按离y=0的距离从小到大，再按直径从大到小
        sorted_boxes = sorted(top_layer_boxes, key=lambda box: (abs(box[1]), -box[6]))
        print("\n[抓取排序调试] 最终排序结果:")
        for i, box in enumerate(sorted_boxes):
            x, y, z = box[:3]
            diameter = box[6]
            y_dist = abs(y)
            print(f"  #{i+1}: 位置({x}, {y}, {z}), |y|={y_dist}, 直径: {diameter}mm")
        return sorted_boxes
    
    def _group_boxes_by_distance(self, boxes_with_diameter):
        """
       
        Args:
            boxes_with_diameter: 包含直径信息的箱子列表
            
        Returns:
            z_groups: 按Z坐标（距离）分组的字典
        """
        if not boxes_with_diameter:
            return {}
        
        # 按Z坐标排序（距离相机从近到远）
        sorted_boxes = sorted(boxes_with_diameter, key=lambda x: x[2])
        for i, box in enumerate(sorted_boxes):
            x, y, z, angle, length, width, box_diameter, box_area = box
            print(f"[直径调试] 分层group: box{i}: x={x}, y={y}, z={z}, length={length}, width={width}, 直径={box_diameter}, 面积={box_area}")
        
        top_box_diameter = sorted_boxes[0][6]  # 第一个箱子的直径
        base_threshold = top_box_diameter * 0.5
        
        z_groups = {}
        current_group = []
        current_z = sorted_boxes[0][2]
        
        
        for i, box in enumerate(sorted_boxes):
            x, y, z, angle, length, width, box_diameter, box_area = box
            
            # 检查是否在中心区域（可能被其他箱子覆盖）
            center_threshold = 450  # 中心区域半径
            is_center_box = abs(x) < center_threshold and abs(y) < center_threshold
            
            # 根据箱子位置微调阈值
            if is_center_box:
                
                threshold = base_threshold * 1.1  
              
            else:
                # 边缘箱子使用标准阈值
                threshold = base_threshold
              
            
            distance_diff = abs(z - current_z)
           
            
            if distance_diff <= threshold:
                current_group.append(box)
              
            else:
                # 保存当前组
                if current_group:
                    z_groups[current_z] = current_group
                  
                
                # 开始新组
                current_group = [box]
                current_z = z
               
        
        # 保存最后一组
        if current_group:
            z_groups[current_z] = current_group
           
        
       
        for z_level in sorted(z_groups.keys()):
            group = z_groups[z_level]
          
        return z_groups
    
    def _sort_boxes_by_three_priorities(self, boxes):
        import numpy as np
        if not boxes:
            return []
        boxes_sorted_by_z = sorted(boxes, key=lambda box: box[2])
        layers = []
        layer = []
        mean_diameter = np.mean([box[5] for box in boxes])
        layer_threshold = 0.8 * mean_diameter
        current_z = boxes_sorted_by_z[0][2]
        for box in boxes_sorted_by_z:
            if abs(box[2] - current_z) <= layer_threshold:
                layer.append(box)
            else:
                layers.append(layer)
                layer = [box]
                current_z = box[2]
        if layer:
            layers.append(layer)
        y_center = 0  # 直接使用y=0作为托盘中心点
        final_order = []
        for layer_idx, layer in enumerate(layers):
            if len(layer) == 5:
                sorted_layer = self._sort_five_boxes_mode(layer, y_center)
            elif len(layer) == 4:
                sorted_layer = self._sort_four_boxes_mode(layer, y_center)
            else:
                sorted_layer = self._sort_general_mode(layer, y_center)
            print(f"[抓取顺序调试] 层{layer_idx+1}排序后:")
            for idx, box in enumerate(sorted_layer):
                print(f"  排序后#{idx+1}: x={box[0]}, y={box[1]}, z={box[2]}, angle={box[3]}, length={box[4]}, width={box[5]}, diameter={box[6]}")
            final_order.extend(sorted_layer)
        return final_order
    
    def _sort_five_boxes_mode(self, boxes, y_center):
        """5箱子模式：绝对优先中心箱子"""
        print("    使用5箱子模式策略")
        
        # 找出中心箱子（Y距离中心最近的）
        center_box = min(boxes, key=lambda box: abs(box[1] - y_center))
        other_boxes = [box for box in boxes if box != center_box]
        
        # 其他箱子按Y距离中心排序
        other_boxes_sorted = sorted(other_boxes, key=lambda box: abs(box[1] - y_center))
        
        result = [center_box] + other_boxes_sorted
        
       
        for i, box in enumerate(other_boxes_sorted):
            print(f"        #{i+2}: 位置({box[0]},{box[1]},{box[2]})mm, Y距离{abs(box[1] - y_center):.1f}mm")
        
        return result
    
    def _sort_four_boxes_mode(self, boxes, y_center):
      
      
        
        # 计算每个箱子到中心的Y距离
        center_threshold = 200  # 中心区域阈值
        center_boxes = []
        edge_boxes = []
        
        for box in boxes:
            y_distance = abs(box[1] - y_center)
            if y_distance <= center_threshold:
                center_boxes.append(box)
            else:
                edge_boxes.append(box)
        
       
        for i, box in enumerate(center_boxes):
            y_distance = abs(box[1] - y_center)
           
        
      
        
      
        for i, box in enumerate(edge_boxes):
            y_distance = abs(box[1] - y_center)
          
        
        # 对中心箱子进行智能排序
        if len(center_boxes) >= 2:
          
            y_distances = [abs(box[1] - y_center) for box in center_boxes]
            y_distance_diff = max(y_distances) - min(y_distances)
          
            
            if y_distance_diff < 50:  # Y距离差异小于50mm时，引入Z坐标判断
            
                
                # 计算Z坐标范围用于归一化
                z_values = [box[2] for box in center_boxes]
                min_z = min(z_values)
                max_z = max(z_values)
                z_range = max_z - min_z if max_z != min_z else 1
                
                # 计算综合得分
                scored_center = []
                for box in center_boxes:
                    y_distance = abs(box[1] - y_center)
                    z_distance = box[2]
                    
                    # Y距离得分（距离越小得分越高）
                    y_score = 1.0 - (y_distance / max(y_distances)) if max(y_distances) > 0 else 1.0
                    
                    # Z距离得分（距离越小得分越高）
                    z_score = 1.0 - ((z_distance - min_z) / z_range) if z_range > 0 else 1.0
                    
                    # 综合得分
                    total_score = 0.7 * y_score + 0.3 * z_score
                    
                    scored_center.append((box, total_score, y_score, z_score))
                  
                
                # 按综合得分排序
                center_boxes_sorted = [box for box, _, _, _ in sorted(scored_center, key=lambda x: x[1], reverse=True)]
                
               
                for i, (box, score, y_score, z_score) in enumerate(sorted(scored_center, key=lambda x: x[1], reverse=True)):
                    print(f"          #{i+1}: 位置({box[0]},{box[1]},{box[2]})mm, 得分{score:.3f}")
            else:
                print(f"        Y距离差异较大({y_distance_diff:.1f}mm ≥ 50mm)，仅使用Y距离排序")
                center_boxes_sorted = sorted(center_boxes, key=lambda box: abs(box[1] - y_center))
        else:
            center_boxes_sorted = sorted(center_boxes, key=lambda box: abs(box[1] - y_center))
        
        # 边缘箱子按Y距离排序
        edge_boxes_sorted = sorted(edge_boxes, key=lambda box: abs(box[1] - y_center))
        
        result = center_boxes_sorted + edge_boxes_sorted
        
        print(f"      最终排序结果:")
        for i, box in enumerate(result):
            y_distance = abs(box[1] - y_center)
           
        
        return result
    
    def _sort_general_mode(self, boxes, y_center=None):
        """
        权重加权排序：y距离中心权重0.8，直径权重0.2，只有y距离中心<30mm时才引入z距离（z权重0.2，y权重0.6，直径0.2）
        """
        import numpy as np
        if y_center is None:
            y_center = 0  # 直接使用y=0作为托盘中心点
        print("    使用权重加权排序策略（托盘中心y=%.2f）" % y_center)
        y_threshold = 30  # 30mm内才允许z参与
        # 归一化参数
        y_dists = [abs(box[1] - y_center) for box in boxes]
        min_y, max_y = min(y_dists), max(y_dists)
        y_range = max_y - min_y if max_y != min_y else 1
        widths = [box[5] for box in boxes]
        min_w, max_w = min(widths), max(widths)
        w_range = max_w - min_w if max_w != min_w else 1
        zs = [box[2] for box in boxes]
        min_z, max_z = min(zs), max(zs)
        z_range = max_z - min_z if max_z != min_z else 1
        scored_boxes = []
        for box in boxes:
            x, y, z, angle, length, width = box[:6]
            y_dist = abs(y - y_center)
            y_score = 1.0 - (y_dist - min_y) / y_range if y_range > 0 else 1.0
            w_score = (width - min_w) / w_range if w_range > 0 else 1.0
            if y_dist < y_threshold:
                z_score = 1.0 - (z - min_z) / z_range if z_range > 0 else 1.0
                score = 0.6 * y_score + 0.2 * z_score + 0.2 * w_score
            else:
                score = 0.8 * y_score + 0.2 * w_score
            scored_boxes.append((box, score, y_score, w_score, z if y_dist < y_threshold else None))
        # 按得分降序排序
        sorted_boxes = [box for box, *_ in sorted(scored_boxes, key=lambda x: x[1], reverse=True)]
        print(f"    排序优先级: Y距离托盘中心(权重0.8) > 直径(权重0.2) > (30mm内)Z(权重0.2)")
        for idx, (box, score, y_score, w_score, z_val) in enumerate(sorted(scored_boxes, key=lambda x: x[1], reverse=True)):
            x, y, z, angle, length, width = box[:6]
            y_dist = abs(y - y_center)
            print(f"[直径调试] 排序后#{idx+1}: x={x}, y={y}, z={z}, length={length}, width={width}, y距离={y_dist}, y_score={y_score}, w_score={w_score}, z={z_val}, 总分={score}")
            if z_val is not None:
                print(f"      排序后#{idx+1}: y距离托盘中心={y_dist:.1f}, y_score={y_score:.3f}, z={z}, w={width}, w_score={w_score:.3f}, 总分={score:.3f}")
            else:
                print(f"      排序后#{idx+1}: y距离托盘中心={y_dist:.1f}, y_score={y_score:.3f}, w={width}, w_score={w_score:.3f}, 总分={score:.3f}")
        return sorted_boxes
    
    def _analyze_box_arrangement(self, boxes, x_center, y_center):
        """
        分析箱子的排列模式
        
        Args:
            boxes: 箱子列表
            x_center, y_center: 几何中心坐标
            
        Returns:
            arrangement_type: 排列模式类型
        """
        if len(boxes) == 1:
            return "single"
        
        # 计算每个箱子到中心的距离
        distances = []
        for box in boxes:
            x, y = box[0], box[1]
            distance = ((x - x_center) ** 2 + (y - y_center) ** 2) ** 0.5
            distances.append(distance)
        
        # 检查是否有中心箱子（距离中心很近的箱子）
        center_threshold = 150
        center_boxes = [d for d in distances if d <= center_threshold]
        
        if len(center_boxes) == 1 and len(boxes) > 1:
            return "center_single"  # 中心单箱子模式
        
        # 检查是否为十字形排列（5个箱子）
        if len(boxes) == 5:
            # 检查是否有4个箱子在四个方向，1个在中心
            x_coords = [box[0] for box in boxes]
            y_coords = [box[1] for box in boxes]
            
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            
            # 检查是否有箱子在四个角落和中心
            corner_count = 0
            center_count = 0
            
            for box in boxes:
                x, y = box[0], box[1]
                # 检查是否在角落
                if (abs(x - x_min) < 100 and abs(y - y_min) < 100) or \
                   (abs(x - x_max) < 100 and abs(y - y_min) < 100) or \
                   (abs(x - x_min) < 100 and abs(y - y_max) < 100) or \
                   (abs(x - x_max) < 100 and abs(y - y_max) < 100):
                    corner_count += 1
                # 检查是否在中心
                elif abs(x - x_center) < 150 and abs(y - y_center) < 150:
                    center_count += 1
            
            if corner_count >= 3 and center_count >= 1:
                return "cross"  # 十字形排列
        
        # 检查是否为正方形排列（4个箱子）
        if len(boxes) == 4:
            # 检查是否形成正方形
            x_coords = [box[0] for box in boxes]
            y_coords = [box[1] for box in boxes]
            
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            
            width = x_max - x_min
            height = y_max - y_min
            
            # 如果宽高相近，可能是正方形排列
            if abs(width - height) < 200:
                return "square"  # 正方形排列
        
        # 检查是否为直线排列
        if len(boxes) >= 3:
            x_coords = [box[0] for box in boxes]
            y_coords = [box[1] for box in boxes]
            
            x_range = max(x_coords) - min(x_coords)
            y_range = max(y_coords) - min(y_coords)
            
            # 如果在一个方向上的范围远大于另一个方向，可能是直线排列
            if x_range > y_range * 2:
                return "line_horizontal"  # 水平直线
            elif y_range > x_range * 2:
                return "line_vertical"  # 垂直直线
        
        # 检查是否为三角形排列（3个箱子）
        if len(boxes) == 3:
            return "triangle"  # 三角形排列
        
        return "irregular"  # 不规则排列
    
    def get_grasping_order_info(self, sorted_boxes):
        """
        获取抓取顺序的详细信息
        
        Args:
            sorted_boxes: 排序后的箱子列表
            
        Returns:
            grasping_info: 抓取信息列表
        """
        grasping_info = []
        
        
        
        for i, box in enumerate(sorted_boxes):
            x, y, z, angle, length, width = box[:6]
            box_diameter = box[6] if len(box) > 6 else width
            box_area = box[7] if len(box) > 7 else length * width
            
            info = {
                'order': i + 1,
                'position': (x, y, z),
                'angle': angle,
                'size': (length, width),
                'box_diameter': box_diameter,
                'box_area': box_area,
                'description': f"第{i+1}个抓取: 位置({x},{y},{z})mm, 角度{angle:.1f}°, 尺寸{length}x{width}mm, 直径{box_diameter}mm, 面积{box_area}mm², 距离相机{z}mm"
            }
            grasping_info.append(info)
            
           
        return grasping_info
    
    def analyze_grasping_strategy(self, boxes):
        """
        分析抓取策略，提供详细的决策过程
        
        Args:
            boxes: 原始检测到的箱子列表
            
        Returns:
            analysis: 策略分析结果
        """
        if not boxes:
            return {"error": "没有检测到箱子"}
        
        # 应用排序算法
        sorted_boxes = self.sort_boxes_for_grasping(boxes)
        
        # 按Z坐标分组分析
        boxes_with_diameter = [box + [box[5], box[4] * box[5]] for box in boxes]
        z_groups = self._group_boxes_by_distance(boxes_with_diameter)
        
        analysis = {
            "total_boxes": len(boxes),
            "layers": len(z_groups),
            "layer_info": {},
            "recommended_order": self.get_grasping_order_info(sorted_boxes),
            "strategy_explanation": []
        }
        
        # 分析每一层
        for z_level in sorted(z_groups.keys()):
            group = z_groups[z_level]
            x_coords = [box[0] for box in group]
            y_coords = [box[1] for box in group]
            
            x_min, x_max = min(x_coords), max(x_coords)
            y_min, y_max = min(y_coords), max(y_coords)
            x_center = (x_min + x_max) / 2
            y_center = (y_min + y_max) / 2
            
            # 计算该层的尺寸统计
            diameters = [box[6] for box in group]
            areas = [box[7] for box in group]
            
            layer_info = {
                "distance_to_camera": z_level,
                "box_count": len(group),
                "x_range": (x_min, x_max),
                "y_range": (y_min, y_max),
                "center": (x_center, y_center),
                "avg_diameter": sum(diameters) / len(diameters),
                "avg_area": sum(areas) / len(areas),
                "boxes": []
            }
            
            for box in group:
                x, y, z, angle, length, width, box_diameter, box_area = box
                distance_to_center = ((x - x_center) ** 2 + (y - y_center) ** 2) ** 0.5
                layer_info["boxes"].append({
                    "position": (x, y, z),
                    "angle": angle,
                    "size": (length, width),
                    "diameter": box_diameter,
                    "area": box_area,
                    "distance_to_center": distance_to_center
                })
            
            analysis["layer_info"][f"layer_{z_level}"] = layer_info
        
        # 生成策略解释
        analysis["strategy_explanation"].append(f"检测到{len(boxes)}个箱子，分为{len(z_groups)}层")
        analysis["strategy_explanation"].append("三层优先级策略:")
        analysis["strategy_explanation"].append("1. 最上层优先（距离相机最近）")
        analysis["strategy_explanation"].append("2. 同层内中间优先（几何中心最近）")
        analysis["strategy_explanation"].append("3. 同层同位置直径大的优先（尺寸大优先）")
        analysis["strategy_explanation"].append("权重分配: 最高点40%, 中间位置40%, 尺寸20%")
        analysis["strategy_explanation"].append("策略说明: 先找最高点确定高度区间，再判断中间区域和直径大小，优先中间")
        
        for z_level in sorted(z_groups.keys()):
            group = z_groups[z_level]
            layer_info = analysis["layer_info"][f"layer_{z_level}"]
            analysis["strategy_explanation"].append(
                f"距离相机{z_level}mm层: {len(group)}个箱子, 平均直径{layer_info['avg_diameter']:.0f}mm, 平均面积{layer_info['avg_area']:.0f}mm²"
            )
        
        analysis["strategy_explanation"].append("总体策略: 从近到远，每层绝对优先抓取中心位置的箱子")
        
        return analysis

    def sort_placement_locations(self, boxes):
   
        if not boxes:
            print("[放置排序调试] 没有检测到可放置位置，返回空列表")
            return []

        # 按z值从大到小排序（z大表示离地面近，优先放置）
        sorted_boxes = sorted(boxes, key=lambda box: box[2], reverse=True)
        
        # 打印排序结果
        print("\n[放置排序调试] 排序结果:")
        for i, box in enumerate(sorted_boxes):
            x, y, z = box[:3]
            print(f"  #{i+1}: 位置({x}, {y}, z={z}) - 离地面距离越近优先放置")
            
            # 检查两侧高度
            side_boxes = []
            side_range = self.task["task"]["width"] * 1.2  # 检查范围稍大于箱子宽度
            for other_box in sorted_boxes:
                if other_box != box:  # 不和自己比较
                    if abs(other_box[0] - x) < 100:  # x方向接近
                        if abs(other_box[1] - y) < side_range:  # 在侧面范围内
                            side_boxes.append(other_box)
            
            if side_boxes:
                max_side_z = max(b[2] for b in side_boxes)
                print(f"    两侧最高点: z={max_side_z}")
                if max_side_z > z:
                    print(f"    需要调整放置高度从{z}到{max_side_z}")

        return sorted_boxes

    def adjust_placement_height(self, grid, location_grid):
        """
        调整放置高度：
        1. 检查放置位置两侧的格子高度
        2. 使用两侧最高点作为放置高度
        """
        x, y = grid['x'], grid['y']
        side_distance = self.task["task"]["width"] * 1.2  # 检查范围稍大于箱子宽度
        
        print(f"\n[高度调试] ====== 计算位置({x}, {y}, z={grid['z']})的放置高度 ======")
        
        # 找到两侧的格子
        left_grids = []
        right_grids = []
        for other_grid in location_grid:
            if other_grid != grid:  # 不和自己比较
                if abs(other_grid['x'] - x) < 100:  # x方向接近
                    if other_grid['y'] < y:  # 左侧格子
                        if abs(other_grid['y'] - y) < side_distance:
                            left_grids.append(other_grid)
                    else:  # 右侧格子
                        if abs(other_grid['y'] - y) < side_distance:
                            right_grids.append(other_grid)
        
        # 打印两侧格子信息
        print(f"[高度调试] 左侧格子数量: {len(left_grids)}")
        for g in left_grids:
            print(f"[高度调试] - 左侧格子: ({g['x']}, {g['y']}, z={g['z']})")
        
        print(f"[高度调试] 右侧格子数量: {len(right_grids)}")
        for g in right_grids:
            print(f"[高度调试] - 右侧格子: ({g['x']}, {g['y']}, z={g['z']})")
        
        # 计算两侧最高点
        left_max_height = max([g['z'] for g in left_grids]) if left_grids else grid['z']
        right_max_height = max([g['z'] for g in right_grids]) if right_grids else grid['z']
        
        print(f"[高度调试] 左侧最高点: z={left_max_height}")
        print(f"[高度调试] 右侧最高点: z={right_max_height}")
        
        # 使用两侧最高点中的较高值
        max_side_height = max(left_max_height, right_max_height)
        print(f"[高度调试] 最终选择的放置高度: z={max_side_height}")
        
        grid['placement_height'] = max_side_height
        return grid['placement_height']

    def get_box_point_cloud(self, np_depth, x, y, box_l, box_w):
        """
        获取指定位置的点云数据并处理
        参数:
            np_depth: 深度图像
            x, y: 中心点坐标
            box_l, box_w: 箱子的长宽
        返回:
            obb: 处理后的点云包围盒，如果点云无效则返回None
        """
        try:
            # 创建点云
            img_depth = o3d.geometry.Image(np_depth)
            pcd = o3d.geometry.PointCloud.create_from_depth_image(
                img_depth,
                self.pinhole_camera_intrinsic,
                depth_scale=1000.0,
                depth_trunc=4.1
            )

            # 设置点云范围
            points = np.asarray(pcd.points)
            min_bound = [
                (x - box_l/2) / 1000,  # 转换为米
                (y - box_w/2) / 1000,
                self.pallet_center["h"]/1000 - 2.0  # 托盘高度以下2米
            ]
            max_bound = [
                (x + box_l/2) / 1000,
                (y + box_w/2) / 1000,
                self.pallet_center["h"]/1000 + 0.15  # 托盘高度以上0.15米
            ]

            # 裁剪点云
            cropped_indices = np.where(((points >= min_bound) & (points <= max_bound)).all(axis=1))[0]
            cropped_point_cloud = pcd.select_by_index(cropped_indices)

            # 点云处理
            nb_neighbors = 15
            std_ratio = 2.0
            voxel_size = 0.03
            cleaned_pcd = cropped_point_cloud.voxel_down_sample(voxel_size)
            cleaned_pcd, ind = cleaned_pcd.remove_statistical_outlier(nb_neighbors, std_ratio)

            # 检查点云是否有效
            if len(cleaned_pcd.points) <= 10:
                return None

            # 获取包围盒
            obb = cleaned_pcd.get_axis_aligned_bounding_box()
            obb.color = (1, 0, 0)

            return obb

        except Exception as e:
            print(f"[调试] get_box_point_cloud发生错误: {str(e)}")
            traceback.print_exc()
            return None

# 读取params.yaml中的local_image_mode参数
def get_local_image_mode_from_yaml():
    yaml_path = os.path.join(os.path.dirname(__file__), 'params.yaml')
    if not os.path.exists(yaml_path):
        return False
    with open(yaml_path, 'r', encoding='utf-8') as f:
        params = yaml.safe_load(f)
    return params.get('local_image_mode', False)


