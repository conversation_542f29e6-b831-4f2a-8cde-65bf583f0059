# 本地图片模式与抓取排序功能使用说明

## 一、功能简介
本项目支持"本地图片模式"，可在无需连接相机的情况下，批量处理指定文件夹下的图片，实现目标检测、抓取顺序智能排序、直径显示与计算等功能。适用于算法调试、批量测试和结果可视化。

---

## 二、使用方法

### 1. 启用本地图片模式
- 在创建 `CameraHandler` 实例时，设置 `use_local_image=True`，并指定 `local_image_folder` 为本地图片文件夹路径。
- 图片文件夹下应包含待处理的 `.ori.jpg` 图片和对应的深度图（如 `_depth.png`）。

**示例代码：**
```python
from frame_get import CameraHandler
import queue

result_queue = queue.Queue()
camera = CameraHandler(
    sn=None,
    id="DC2",
    result_queue=result_queue,
    use_local_image=True,
    local_image_folder="D:/your/image/folder"
)
camera.run_camera_in_main_thread()
```

### 2. 主要流程
- 自动遍历图片文件夹，依次读取图片和深度图
- 对每张图片进行推理、抓取排序、直径计算与可视化
- 结果图片和CSV报告自动保存到 `processed_results` 文件夹

---

## 三、关键函数与参数说明

### 1. 本地图片读取与处理
- `get_images()`
  - 自动遍历本地图片文件夹，读取图片和对应深度图
  - 返回图片帧列表，供后续推理处理

- `process_images(image_list)`
  - 对图片帧进行推理、抓取排序、直径计算和结果保存
  - 支持结果图片和CSV报告自动保存

### 2. 智能抓取排序
- `sort_boxes_for_grasping(boxes)`
  - 输入：`boxes`，格式为`[[x, y, z, angle, length, width], ...]`
  - 输出：排序后的箱体列表，优先级从高到低
  - 排序策略：
    1. 最上层优先（z最小）
    2. 同层中心优先（y距离中心最小）
    3. 同层同位置直径大优先

- `get_grasping_order_info(sorted_boxes)`
  - 输入：排序后的箱体列表
  - 输出：每个箱子的抓取顺序、位置、角度、尺寸等详细信息

### 3. 直径显示与计算
- 直径计算与显示在 `YOLOv8Seg.draw_and_visualize` 方法中实现：
  - 自动在检测目标上绘制直径测量线、中心点、抓取顺序编号等
  - 直径单位为毫米，基于深度图和相机内参自动换算

---

## 四、典型流程

1. 启动本地图片模式：
   - 设置 `use_local_image=True`，指定 `local_image_folder` 路径
2. 运行 `run_camera_in_main_thread()`，自动批量处理所有图片
3. 每张图片经过推理、排序、直径计算，结果图片和CSV报告自动保存
4. 可在 `processed_results` 文件夹查看识别结果和抓取顺序可视化

---

## 五、注意事项
- 图片和深度图需命名规范（如 `xxx_ori.jpg` 和 `xxx_depth.png`）
- 结果文件自动保存在 `processed_results` 文件夹
- 算法参数可在 `config.py` 或 `params.yaml` 中调整
- 支持多种抓取排序策略，详见 `CameraHandler` 类相关方法
- 直径显示依赖深度图和相机内参，需保证数据准确

---

## 六、详细函数调用说明

### 1. 本地图片模式核心函数

#### 1.1 CameraHandler 类初始化
**位置：** `frame_get.py` 第 38-120 行
```python
def __init__(self, sn, id, result_queue, use_local_image=False, local_image_folder=None):
    # 第 60-85 行：本地图片模式初始化
    if use_local_image and local_image_folder:
        # 创建输出文件夹
        output_folder = os.path.join(os.path.dirname(local_image_folder), "processed_results")
        # 获取所有.ori.jpg文件
        self.image_files = []
        for f in all_files:
            if f_lower.endswith('.ori.jpg') or 'ori.jpg' in f_lower:
                self.image_files.append(f)
```

#### 1.2 图片读取函数
**位置：** `frame_get.py` 第 222-280 行
```python
def get_images(self):
    if self.use_local_image and self.local_image_folder:
        # 第 225-235 行：读取原图
        img_path = os.path.join(self.local_image_folder, self.image_files[self.image_index])
        img = cv2.imdecode(np.fromfile(img_path_abs, dtype=np.uint8), cv2.IMREAD_COLOR)
        
        # 第 240-255 行：查找对应深度图
        if original_filename.endswith('_ori.jpg'):
            depth_filename = original_filename.replace('_ori.jpg', '_depth.png')
        depth_path = os.path.join(self.local_image_folder, depth_filename)
        
        # 第 260-270 行：返回DummyFrame对象
        class DummyFrame:
            def __init__(self, img, depthimg):
                self.streamID = PERCIPIO_STREAM_COLOR
                self.img = img
                self.depthimg = depthimg
        return [DummyFrame(img, depthimg)]
```

#### 1.3 图片处理函数
**位置：** `frame_get.py` 第 281-450 行
```python
def process_images(self, image_list):
    # 第 285-295 行：本地图片模式处理
    if self.use_local_image and self.local_image_folder:
        for idx, frame in enumerate(image_list):
            # 第 300-310 行：模型推理
            boxes, segments, _ = config.box_model(frame.img, conf_threshold=0.35, iou_threshold=0.45)
            
            # 第 315-325 行：应用排序算法
            if result and len(result) > 0:
                boxes_for_sorting = []
                for box_result in result:
                    x, y, z, angle, px, py, short_edge, long_edge = box_result
                    boxes_for_sorting.append([x, y, z, angle, long_edge, short_edge])
                
                # 第 330-340 行：调用排序函数
                sorted_boxes = self.sort_boxes_for_grasping(boxes_for_sorting)
                grasping_info = self.get_grasping_order_info(sorted_boxes)
                
                # 第 345-365 行：创建抓取顺序映射
                grasping_order_map = {}
                for info in grasping_info:
                    for i, original_box in enumerate(result):
                        if (abs(original_box[0] - info['position'][0]) < 50 and 
                            abs(original_box[1] - info['position'][1]) < 50 and 
                            abs(original_box[2] - info['position'][2]) < 50):
                            grasping_order_map[i] = info['order']
                            break
                
                # 第 370-380 行：重新画图包含抓取顺序
                output_image, result = config.box_model.draw_and_visualize(
                    frame.img, boxes, segments, frame.depthimg, default_depth_calib_intr, 
                    vis=False, save=False, grasping_order_map=grasping_order_map
                )
            
            # 第 385-400 行：保存结果图片
            original_filename = self.image_files[self.image_index - 1]
            base_name = original_filename.replace('.ori.jpg', '')
            output_filename = f"{base_name}_result.jpg"
            save_path = os.path.join(self.output_folder, output_filename)
            
            # 第 405-415 行：保存CSV报告
            datas = {
                "dev": self.id,
                "type": "box",
                "result": result,
                "image_id": self.image_index,
                "local_image_path": save_path
            }
            self.save_csv(output_image, datas, self.image_index, self.start_time)
```

### 2. 抓取排序算法函数

#### 2.1 主排序函数
**位置：** `frame_get.py` 第 1050-1070 行
```python
def sort_boxes_for_grasping(self, boxes):
    """
    简化抓取排序算法 - 只抓取最高层箱子
    优先级：1.最上层优先 2.同层内中间优先 3.同层同位置直径大的优先
    """
    if not boxes:
        print("没有检测到箱子，返回空列表")
        return []
    
    # 第 1055-1060 行：添加直径和面积信息
    boxes_with_diameter = []
    for i, box in enumerate(boxes):
        x, y, z, angle, length, width = box
        box_diameter = width
        box_area = length * width
        boxes_with_diameter.append(box + [box_diameter, box_area])
    
    # 第 1065-1070 行：按Z坐标分组
    z_groups = self._group_boxes_by_distance(boxes_with_diameter)
    if z_groups:
        top_layer_z = min(z_groups.keys())
        top_layer_boxes = z_groups[top_layer_z]
        sorted_boxes = self._sort_boxes_by_three_priorities(top_layer_boxes)
    else:
        sorted_boxes = []
    return sorted_boxes
```

#### 2.2 Z坐标分组函数
**位置：** `frame_get.py` 第 1075-1120 行
```python
def _group_boxes_by_distance(self, boxes_with_diameter):
    """
    按Z坐标（距离）分组箱子
    """
    if not boxes_with_diameter:
        return {}
    
    # 第 1080-1085 行：按Z坐标排序
    sorted_boxes = sorted(boxes_with_diameter, key=lambda x: x[2])
    
    # 第 1090-1095 行：计算分组阈值
    top_box_diameter = sorted_boxes[0][6]  # 第一个箱子的直径
    base_threshold = top_box_diameter * 0.5
    
    # 第 1100-1120 行：分组逻辑
    z_groups = {}
    current_group = []
    current_z = sorted_boxes[0][2]
    
    for i, box in enumerate(sorted_boxes):
        x, y, z, angle, length, width, box_diameter, box_area = box
        
        # 检查是否在中心区域
        center_threshold = 400  # 中心区域半径
        is_center_box = abs(x) < center_threshold and abs(y) < center_threshold
        
        # 根据箱子位置微调阈值
        if is_center_box:
            threshold = base_threshold * 1.1
        else:
            threshold = base_threshold
        
        distance_diff = abs(z - current_z)
        
        if distance_diff <= threshold:
            current_group.append(box)
        else:
            # 保存当前组
            if current_group:
                z_groups[current_z] = current_group
            # 开始新组
            current_group = [box]
            current_z = z
    
    # 保存最后一组
    if current_group:
        z_groups[current_z] = current_group
    
    return z_groups
```

#### 2.3 三层优先级排序函数
**位置：** `frame_get.py` 第 1125-1180 行
```python
def _sort_boxes_by_three_priorities(self, boxes):
    """
    三层优先级排序：1.最上层 2.中间位置 3.直径大小
    """
    if not boxes:
        return []
    
    # 第 1130-1140 行：按Z坐标分层
    boxes_sorted_by_z = sorted(boxes, key=lambda box: box[2])
    layers = []
    layer = []
    mean_diameter = np.mean([box[5] for box in boxes])
    layer_threshold = 0.8 * mean_diameter
    current_z = boxes_sorted_by_z[0][2]
    
    for box in boxes_sorted_by_z:
        if abs(box[2] - current_z) <= layer_threshold:
            layer.append(box)
        else:
            layers.append(layer)
            layer = [box]
            current_z = box[2]
    if layer:
        layers.append(layer)
    
    # 第 1145-1180 行：每层内排序
    y_center = 0  # 托盘中心点
    final_order = []
    for layer_idx, layer in enumerate(layers):
        if len(layer) == 5:
            sorted_layer = self._sort_five_boxes_mode(layer, y_center)
        elif len(layer) == 4:
            sorted_layer = self._sort_four_boxes_mode(layer, y_center)
        else:
            sorted_layer = self._sort_general_mode(layer, y_center)
        final_order.extend(sorted_layer)
    return final_order
```

#### 2.4 特殊模式排序函数
**位置：** `frame_get.py` 第 1185-1250 行

**5箱子模式：**
```python
def _sort_five_boxes_mode(self, boxes, y_center):
    """5箱子模式：绝对优先中心箱子"""
    # 第 1190-1195 行：找出中心箱子
    center_box = min(boxes, key=lambda box: abs(box[1] - y_center))
    other_boxes = [box for box in boxes if box != center_box]
    
    # 第 1200-1205 行：其他箱子按Y距离排序
    other_boxes_sorted = sorted(other_boxes, key=lambda box: abs(box[1] - y_center))
    result = [center_box] + other_boxes_sorted
    return result
```

**4箱子模式：**
```python
def _sort_four_boxes_mode(self, boxes, y_center):
    """4箱子模式：中心箱子优先，边缘箱子次之"""
    # 第 1210-1220 行：分离中心箱子和边缘箱子
    center_threshold = 200
    center_boxes = []
    edge_boxes = []
    
    for box in boxes:
        y_distance = abs(box[1] - y_center)
        if y_distance <= center_threshold:
            center_boxes.append(box)
        else:
            edge_boxes.append(box)
    
    # 第 1225-1250 行：智能排序逻辑
    if len(center_boxes) >= 2:
        y_distances = [abs(box[1] - y_center) for box in center_boxes]
        y_distance_diff = max(y_distances) - min(y_distances)
        
        if y_distance_diff < 50:  # Y距离差异小时，引入Z坐标判断
            # 计算综合得分
            scored_center = []
            for box in center_boxes:
                y_distance = abs(box[1] - y_center)
                z_distance = box[2]
                
                # Y距离得分（距离越小得分越高）
                y_score = 1.0 - (y_distance / max(y_distances)) if max(y_distances) > 0 else 1.0
                
                # Z距离得分（距离越小得分越高）
                z_score = 1.0 - ((z_distance - min_z) / z_range) if z_range > 0 else 1.0
                
                # 综合得分
                total_score = 0.7 * y_score + 0.3 * z_score
                scored_center.append((box, total_score, y_score, z_score))
            
            # 按综合得分排序
            center_boxes_sorted = [box for box, _, _, _ in sorted(scored_center, key=lambda x: x[1], reverse=True)]
        else:
            center_boxes_sorted = sorted(center_boxes, key=lambda box: abs(box[1] - y_center))
    else:
        center_boxes_sorted = sorted(center_boxes, key=lambda box: abs(box[1] - y_center))
    
    # 边缘箱子按Y距离排序
    edge_boxes_sorted = sorted(edge_boxes, key=lambda box: abs(box[1] - y_center))
    result = center_boxes_sorted + edge_boxes_sorted
    return result
```

### 3. 直径显示与计算函数

#### 3.1 直径计算核心函数
**位置：** `YoloOnnx.py` 第 350-450 行
```python
def draw_and_visualize(self, im, bboxes, segments, depthimg, depth_calib_intr, vis=True, save=False, xyz_range=None, grasping_order_map=None):
    # 第 360-380 行：遍历每个检测框
    for idx, ((*box, conf, cls_), segment) in enumerate(zip(bboxes, segments)):
        # 第 385-395 行：计算最小外接矩形
        rect = cv2.minAreaRect(np.array(segment))
        width = rect[1][0]
        height = rect[1][1]
        shot_edge = min(width, height)
        if width > height:
            long_edge = width
            angle = rect[2]
        else:
            long_edge = height
            angle = rect[2] - 90
        
        # 第 400-410 行：获取中心点
        minbox = cv2.boxPoints(rect)
        minbox = np.array(minbox, dtype=np.int32)
        center = (int(rect[0][0]), int(rect[0][1]))
        
        # 第 415-425 行：获取深度值
        if depthimg is not None:
            if 0 <= center[1] < depthimg.shape[0] and 0 <= center[0] < depthimg.shape[1]:
                center_depth = depthimg[center[1], center[0]]
        
        # 第 430-450 行：直径计算采样
        mask_h, mask_w = mask.shape
        rect_center = np.array(rect[0])
        rect_size = rect[1]
        rect_angle = rect[2]
        
        if rect_size[0] < rect_size[1]:
            theta = np.deg2rad(rect_angle)
        else:
            theta = np.deg2rad(rect_angle + 90)
        
        normal_vec = np.array([np.cos(theta), np.sin(theta)])
        tangent_vec = np.array([-normal_vec[1], normal_vec[0]])
        
        # 采样点：中心、中心+50像素、中心-50像素（沿切线方向）
        sample_points = [
            rect_center,
            rect_center + tangent_vec * 50,
            rect_center - tangent_vec * 50
        ]
        
        # 第 455-480 行：计算直径
        diameters_pixel = []
        centers_2d = []
        for idx, pt in enumerate(sample_points):
            pt = np.clip(pt, [0,0], [mask_w-1, mask_h-1])
            
            def find_edge_along_vec(center, vec, mask, max_search=300):
                base = np.array(center, dtype=np.float32)
                for d in range(0, max_search):
                    p = base + vec * d
                    x, y = int(round(p[0])), int(round(p[1]))
                    if x < 0 or x >= mask_w or y < 0 or y >= mask_h or mask[y, x] == 0:
                        return (int(np.clip(round(base[0] + vec[0]*(d-1)), 0, mask_w-1)), 
                                int(np.clip(round(base[1] + vec[1]*(d-1)), 0, mask_h-1)))
                return (int(np.clip(round(base[0] + vec[0]*max_search), 0, mask_w-1)), 
                        int(np.clip(round(base[1] + vec[1]*max_search), 0, mask_h-1)))
            
            pt_pos = find_edge_along_vec(pt, normal_vec, mask)
            pt_neg = find_edge_along_vec(pt, -normal_vec, mask)
            
            # 绘制直径线
            color = (0,0,255) if idx==0 else (255,0,0)
            cv2.line(im, pt_pos, pt_neg, color, 3)
            
            center_pt = (int((pt_pos[0] + pt_neg[0]) / 2), int((pt_pos[1] + pt_neg[1]) / 2))
            centers_2d.append(center_pt)
            
            # 计算像素距离并转换为实际直径
            dx = pt_pos[0] - pt_neg[0]
            dy = pt_pos[1] - pt_neg[1]
            pixel_dist = np.sqrt(dx*dx + dy*dy)
            
            if center_depth is not None and center_depth > 0:
                fx = depth_calib_intr[0]
                diam_pixel = pixel_dist * center_depth / fx
                diameters_pixel.append(diam_pixel)
                
                # 显示直径数值
                if idx != 0:
                    cv2.putText(im, f"{int(round(diam_pixel))}", 
                               (center_pt[0]+5, center_pt[1]-5), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255,0,0), 2)
        
        # 第 485-495 行：计算平均直径
        valid_diams = [d for d in diameters_pixel if d > 0]
        avg_diameter_pixel = int(round(sum(valid_diams)/len(valid_diams))) if valid_diams else 0
        avg_center_2d = tuple(int(round(x)) for x in np.mean(centers_2d, axis=0))
        
        # 显示最终结果
        cv2.circle(im, avg_center_2d, 10, (0,0,255), -1)
        cv2.putText(im, f"{avg_diameter_pixel}", 
                   (avg_center_2d[0]+5, avg_center_2d[1]-5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2)
        cv2.putText(im, f"({x},{y},{z})", 
                   (avg_center_2d[0]+5, avg_center_2d[1]+25), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,0,255), 2)
        
        # 返回结果
        result.append([x, y, z, -angle, int(avg_center_2d[0]), int(avg_center_2d[1]), avg_diameter_pixel, long_edge])
```

#### 3.2 抓取顺序显示函数
**位置：** `YoloOnnx.py` 第 500-520 行
```python
# 显示抓取顺序编号
grasping_order_text = ""
if grasping_order_map is not None and idx in grasping_order_map:
    order_num = grasping_order_map[idx]
    grasping_order_text = f"#{order_num}"
    
    # 在箱子右侧显示抓取顺序编号，使用红色
    text_x = int(center[0]) + 360  # 向右偏移360像素
    text_y = int(center[1])        # 与箱子中心同高度
    cv2.putText(im, grasping_order_text, (text_x, text_y), 
               cv2.FONT_HERSHEY_SIMPLEX, 1.5, (0, 0, 255), 3)  # 红色 (0, 0, 255)
    
    # 在编号周围画一个红色圆圈
    cv2.circle(im, (text_x + 15, text_y - 10), 25, (0, 0, 255), 2)  # 红色圆圈
```

### 4. 结果保存函数

#### 4.1 CSV报告保存函数
**位置：** `frame_get.py` 第 1400-1450 行
```python
def save_csv(self, img, datas, image_id, start_time, img_ori=None, img_depth=None):
    # 第 1405-1415 行：准备保存数据
    save_data = {}
    end_time = time.time()
    useT_time = end_time - start_time
    save_time = time.strftime('_%Y_%m_%d_%H_%M_%S')
    datas["useT_time"] = useT_time
    datas["save_time"] = save_time
    datas["image_id"] = image_id
    
    # 第 1420-1425 行：处理task数据
    if 'task' in datas:
        for key, value in datas['task'].items():
            datas[key] = value
        del datas['task']
    
    # 第 1430-1440 行：创建保存路径
    save_name = str(datas["dev"]) + "-" + str(datas["type"]) + save_time + "-" + str(image_id)
    directory = "./data"
    today = datetime.datetime.now().strftime("%Y%m%d")
    folderPath = os.path.join(directory, today)
    
    if not os.path.exists(folderPath):
        os.makedirs(folderPath)
    
    # 第 1445-1450 行：保存图片和CSV
    save_path = os.path.join(folderPath, save_name + ".jpg")
    cv2.imwrite(save_path, img)
    
    if img_ori is not None:
        save_path = os.path.join(folderPath, save_name + "_ori" + ".jpg")
        cv2.imwrite(save_path, img_ori)
    
    if img_depth is not None:
        save_path = os.path.join(folderPath, save_name + "_depth" + ".png")
        cv2.imwrite(save_path, img_depth)
    
    WriteDataFile = os.path.join(folderPath, 'Data.csv')
    with open(WriteDataFile, "a+", newline='') as f:
        writer = csv.writer(f)
        writer.writerow(datas.values())
        f.close()
```

### 5. 主程序调用流程

#### 5.1 程序入口
**位置：** `robot.py` 第 80-120 行
```python
def main():
    # 第 85-90 行：读取配置
    config.reparam()
    config.loginit()
    
    # 第 95-100 行：创建相机实例
    LOCAL_IMAGE_MODE = config.local_image_mode
    result_queue = queue.Queue()
    cameras = {
        "DC2": CameraHandler(None, "DC2", result_queue, 
                           use_local_image=LOCAL_IMAGE_MODE, 
                           local_image_folder="D:/companycode/bugcollection/6.30-7.6视觉BUG汇总/bugsortup" 
                           if LOCAL_IMAGE_MODE else None),
    }
    
    # 第 105-120 行：启动处理
    if LOCAL_IMAGE_MODE:
        print(f"\n[ROBOT调试] ====== 本地图片模式启动 ======")
        for camera in cameras.values():
            camera.run_camera_in_main_thread()
        print("[ROBOT调试] 本地图片模式识别完成，按任意键退出")
```

#### 5.2 相机运行函数
**位置：** `frame_get.py` 第 1300-1320 行
```python
def run_camera_in_main_thread(self):
    if self.use_local_image and self.local_image_folder:
        print('进入本地图片识别模式')
        while True:
            # 第 1305-1310 行：获取图片
            image_list = self.get_images()
            if image_list:
                print('get_images 返回图片，调用 process_images')
                self.process_images(image_list)
            else:
                print('get_images 返回 None，跳过本次')
                break  # 本地图片处理完毕后退出
        return
```

---

## 七、调试信息说明

### 1. 调试输出位置
- **YOLO调试信息：** `YoloOnnx.py` 第 350-360 行
- **相机模式调试：** `frame_get.py` 第 450-500 行
- **抓取顺序调试：** `frame_get.py` 第 1050-1100 行
- **ROBOT调试：** `robot.py` 第 40-80 行

### 2. 关键调试信息
```python
# YOLO推理调试
print(f"[YOLO调试] 检测到{len(bboxes)}个目标")
print(f"[YOLO调试] 图像尺寸: {im.shape if im is not None else 'None'}")
print(f"[YOLO调试] 深度图尺寸: {depthimg.shape if depthimg is not None else 'None'}")

# 抓取排序调试
print(f"[抓取顺序调试] 最高层Z={top_layer_z}，箱子数量={len(top_layer_boxes)}")
print(f"[抓取顺序调试] 排序后的箱子: {sorted_boxes}")

# 相机模式调试
print(f"[相机模式调试] 接收到的image_list长度: {len(image_list)}")
print(f"[相机模式调试] 调用get_img_result处理图像")
```

### 3. 错误处理
- **异常捕获：** 所有关键函数都包含 try-except 块
- **错误日志：** 使用 `config.log.logger.error()` 记录错误
- **调试输出：** 使用 `print()` 输出调试信息
- **状态检查：** 检查关键变量是否为 None

---
